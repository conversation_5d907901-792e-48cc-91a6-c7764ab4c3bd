<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.OperationLogInfoMapper">
    <!-- 通过字典code获取字典数据 -->
    <select id="queryDictTextByKey" parameterType="String"  resultType="String">
        select s.item_text from sys_dict_item s
        where s.dict_id = (select id from sys_dict where dict_code = #{code})
        and s.item_value = #{key}
    </select>
</mapper>