<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ElecProtocolMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.ElecProtocol">
        SELECT * FROM ELEC_PROTOCOL
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ORDER BY CREATE_DATE DESC
    </select>
</mapper>
