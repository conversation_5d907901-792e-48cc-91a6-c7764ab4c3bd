<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.EmsStocksFlowMapper">

    <select id="listGoodsFlow" resultType="org.jeecg.modules.business.entity.EmsStocksFlow">
        SELECT
            *,
            IFNULL(STOCK_VAR, 0) STOCK_VAR,
            IFNULL(OCCUPY_VAR, 0) OCCUPY_VAR
        FROM
            `pts_ems_stocks_flow`
        WHERE
            EMS_NO = #{emsNo}
            AND G_NO = #{gNo}
        <choose>
            <when test="copGno != null and copGno != ''">
                AND COP_GNO = #{copGno}
            </when>
            <otherwise>
                AND (COP_GNO IS NULL OR COP_GNO = '')
            </otherwise>
        </choose>
        ORDER BY
            CREATE_DATE DESC
    </select>
</mapper>
