<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.SyncNemsInvtHeadMapper">

    <select id="selectUnClosed" resultType="org.jeecg.modules.business.entity.SyncNemsInvtHead">
        SELECT
            *
        FROM
            `sync_nems_invt_head`
        WHERE
            TENANT_ID = #{tenantId}
            AND VRFDED_MARKCD IN ( '0', '1' )
        <if test="startTime != null and startTime != ''">
            AND date_format(CREATE_DATE, '%Y-%m-%d') &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND date_format(CREATE_DATE, '%Y-%m-%d') &lt;= #{endTime}
        </if>
        ORDER BY
            CREATE_DATE DESC
    </select>
</mapper>
