<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.BillingReportMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.BillingReport">
        SELECT
            *
        FROM
            `billing_report`
        <where>
            <if test="billingReport.settleNo != null and billingReport.settleNo != ''">
                AND SETTLE_NO LIKE CONCAT('%', #{billingReport.settleNo}, '%')
            </if>
            <if test="billingReport.storeCode != null and billingReport.storeCode != ''">
                AND STORE_CODE = #{billingReport.storeCode}
            </if>
            <if test="billingReport.customer != null and billingReport.customer != ''">
                AND CUSTOMER = #{billingReport.customer}
            </if>
            <if test="billingReport.costCode != null and billingReport.costCode != ''">
                AND COST_CODE = #{billingReport.costCode}
            </if>
            <if test="billingReport.starReportDate != null and billingReport.starReportDate != ''">
                AND date_format(REPORT_DATE, '%Y-%m-%d') &gt;= #{billingReport.starReportDate}
            </if>
            <if test="billingReport.endReportDate != null and billingReport.endReportDate != ''">
                AND date_format(REPORT_DATE, '%Y-%m-%d') &lt;= #{billingReport.endReportDate}
            </if>
        </where>
        ORDER BY REPORT_DATE DESC
    </select>
</mapper>
