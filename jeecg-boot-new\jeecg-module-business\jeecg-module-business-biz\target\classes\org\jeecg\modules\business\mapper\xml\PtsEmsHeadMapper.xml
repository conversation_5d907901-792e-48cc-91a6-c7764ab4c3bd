<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PtsEmsHeadMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.PtsEmsHead">
        SELECT
            a.*
        FROM
            `pts_ems_head` a
        <where>
            a.TENANT_ID = #{ptsEmsHead.tenantId}
            <if test="ptsEmsHead.emsNo != null and ptsEmsHead.emsNo != ''">
                AND a.EMS_NO LIKE CONCAT('%', #{ptsEmsHead.emsNo}, '%')
            </if>
            <if test="ptsEmsHead.tradeName != null and ptsEmsHead.tradeName != ''">
                AND a.TRADE_NAME LIKE CONCAT('%', #{ptsEmsHead.tradeName}, '%')
            </if>
            <if test="ptsEmsHead.copEmsNo != null and ptsEmsHead.copEmsNo != ''">
                AND a.COP_EMS_NO LIKE CONCAT('%', #{ptsEmsHead.copEmsNo}, '%')
            </if>
            <if test="ptsEmsHead.seqNo != null and ptsEmsHead.seqNo != ''">
                AND a.SEQ_NO LIKE CONCAT('%', #{ptsEmsHead.seqNo}, '%')
            </if>
            <if test="ptsEmsHead.status != null and ptsEmsHead.status != ''">
                AND a.STATUS = #{ptsEmsHead.status}
            </if>
            <if test="ptsEmsHead.startEndDate != null and ptsEmsHead.startEndDate != ''">
                AND a.END_DATE&gt;= CONCAT('','${ptsEmsHead.startEndDate}',' 00:00:00')
            </if>
            <if test="ptsEmsHead.lastEndDate != null and ptsEmsHead.lastEndDate != ''">
                AND a.END_DATE &lt;= CONCAT('','${ptsEmsHead.lastEndDate}',' 23:59:59')
            </if>
            <if test="ptsEmsHead.inputStartDate != null and ptsEmsHead.inputStartDate != ''">
                AND a.INPUT_DATE&gt;= CONCAT('','${ptsEmsHead.inputStartDate}',' 00:00:00')
            </if>
            <if test="ptsEmsHead.inputLastDate != null and ptsEmsHead.inputLastDate != ''">
                AND a.INPUT_DATE &lt;= CONCAT('','${ptsEmsHead.inputLastDate}',' 23:59:59')
            </if>
            <if test="ptsEmsHead.startDeclareDate != null and ptsEmsHead.startDeclareDate != ''">
                AND a.DECLARE_DATE&gt;= CONCAT('','${ptsEmsHead.startDeclareDate}',' 00:00:00')
            </if>
            <if test="ptsEmsHead.lastDeclareDate != null and ptsEmsHead.lastDeclareDate != ''">
                AND a.DECLARE_DATE &lt;= CONCAT('','${ptsEmsHead.lastDeclareDate}',' 23:59:59')
            </if>
            <choose>
                <when test="ptsEmsHead.menuType != null and ptsEmsHead.menuType != '' and ptsEmsHead.menuType == 1">
                    AND a.EMS_TYPE IN ('B', 'C')
                </when>
                <when test="ptsEmsHead.menuType != null and ptsEmsHead.menuType != '' and ptsEmsHead.menuType == 2">
                    AND a.EMS_TYPE IN ('1', '2', '3', '4','5')
                </when>
                <when test="ptsEmsHead.menuType != null and ptsEmsHead.menuType != '' and ptsEmsHead.menuType == 3">
                    AND a.EMS_TYPE IN ('TW', 'L')
                </when>
            </choose>
        </where>
        ORDER BY a.BY_DATE DESC
    </select>
    <select id="listDeclarationRecord"
            resultType="org.jeecg.modules.business.entity.excel.ExportDeclarationRecordExcel">
        SELECT NEMS_INVT_LIST.PUTREC_SEQNO gNo,NEMS_INVT_HEAD.BOND_INVT_NO,NEMS_INVT_HEAD.ETPS_INNER_INVT_NO,
        VRFDED_MARKCD,INVT_DCL_TIME,IMPEXP_MARKCD,DCL_QTY,
        DEC_HEAD.CLEARANCE_NO dclEtpsno,DEC_HEAD.APP_DATE declarationDate
        FROM NEMS_INVT_HEAD
        LEFT JOIN NEMS_INVT_LIST ON NEMS_INVT_HEAD.ID = NEMS_INVT_LIST.INV_ID
        LEFT JOIN DEC_HEAD ON DEC_HEAD.INV_ID=NEMS_INVT_HEAD.ID
        WHERE NEMS_INVT_HEAD.PUTREC_NO = #{putrecNo}
        AND NEMS_INVT_LIST.PUTREC_SEQNO in
        <foreach collection="gNos" item="item" open="(" close=")" separator=",">
            (#{item})
        </foreach>
        AND NEMS_INVT_HEAD.MTPCK_ENDPRD_MARKCD = #{type}
        AND (NEMS_INVT_HEAD.STATUS IS NULL OR NEMS_INVT_HEAD.STATUS &lt;&gt;'E')
        AND NEMS_INVT_HEAD.DCL_TYPECD != '3'
    </select>
    <select id="listDeclarationStockRecord"
            resultType="org.jeecg.modules.business.entity.excel.ExportDeclarationRecordStockExcel">
        select sh.DEC_STATUS decStatus,sh.SAS_STOCK_NO sasStockNo,sh.ETPS_PREENT_NO etpsPreentNo,
        sh.STOCK_TYPECD stockTypecd,sh.DECLARATION_DATE declarationDate,SUM(sg.DCL_QTY) AS dclQty,
        nh.BOND_INVT_NO bondInvtNo,nh.ETPS_INNER_INVT_NO etpsInnerInvtNo,nh.INVT_DCL_TIME invtDclTime,
        dh.CLEARANCE_NO dclNo,dh.APP_DATE dclDate
        from STOCK_HEAD_TYPE sh
        left join STOCK_GOODS_TYPE sg on sh.ID = sg.STOCK_ID
        left join NEMS_INVT_HEAD nh on nh.id = sh.INVT_HEAD_ID
        left join DEC_HEAD dh on dh.INV_ID = nh.ID
        where sh.AREAIN_ORIACT_NO = #{putrecNo}
        and sg.ORIACT_GDS_SEQNO in
        <foreach collection="gNos" item="item" open="(" close=")" separator=",">
            (#{item})
        </foreach>
    </select>
    <select id="gNoQtyStatistics" resultType="java.util.HashMap">
        SELECT nl.PUTREC_SEQNO gNo,SUM(nl.DCL_QTY) num
        FROM NEMS_INVT_HEAD nh INNER JOIN NEMS_INVT_LIST nl ON nh.ID = nl.INV_ID
        WHERE nh.VRFDED_MARKCD='1'
          AND nh.IMPEXP_MARKCD = 'E'
          AND nh.PUTREC_NO = 'L4301D18A001'
          AND nh.DCL_TYPECD != '3'
         AND ( nh.STATUS != 'E' OR nh.`STATUS` IS NULL OR nh.`STATUS` = '' )
        GROUP BY nl.PUTREC_SEQNO
    </select>
    <select id="checkInventoryByNem" resultType="java.util.HashMap">
        SELECT nl.PUTREC_SEQNO gNo,
        sum(case nh.IMPEXP_MARKCD when 'I' then nl.DCL_QTY else -nl.DCL_QTY end) num
        FROM NEMS_INVT_HEAD nh INNER JOIN NEMS_INVT_LIST nl ON nh.ID = nl.INV_ID
        <where>
            nh.PUTREC_NO = #{emsNo}
            AND nl.PUTREC_SEQNO IS NOT NULL
            AND nl.PUTREC_SEQNO &lt;&gt; ''
            AND nh.DCL_TYPECD &lt;&gt; '3'
            AND nh.STATUS &lt;&gt; 'E'
            <if test="gNos != null and gNos.size > 0">
                and nl.PUTREC_SEQNO in
                <foreach collection="gNos" item="item" index="i" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY nl.PUTREC_SEQNO
        ORDER BY nl.PUTREC_SEQNO
    </select>
    <select id="checkInventoryByEms" resultType="java.util.HashMap">
        SELECT pea.G_NO gNo,pea.STOCK_QTY-pea.OCCUPY_QTY num
        FROM PTS_EMS_AIMG pea
        <where>
            pea.EMS_NO = #{emsNo}
            <if test="gNos != null and gNos.size > 0">
                and pea.G_NO in
                <foreach collection="gNos" item="item" index="i" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
