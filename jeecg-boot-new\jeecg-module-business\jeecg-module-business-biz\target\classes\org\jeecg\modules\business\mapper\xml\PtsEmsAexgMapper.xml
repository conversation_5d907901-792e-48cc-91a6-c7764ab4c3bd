<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PtsEmsAexgMapper">

    <select id="listEmsDetail" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
            *
        FROM
            pts_ems_aexg LEFT JOIN pts_ems_head on pts_ems_head.ID = pts_ems_aexg.EMS_ID
        <where>
            <if test="emsQueryDto.tenantId != null and emsQueryDto.tenantId != ''">
                AND pts_ems_head.TENANT_ID = #{emsQueryDto.tenantId}
            </if>
            <if test="emsQueryDto.emsId!= null and emsQueryDto.emsId!= ''">
                AND pts_ems_aexg.EMS_ID = #{emsQueryDto.emsId}
            </if>
            <if test="emsQueryDto.emsNo!= null and emsQueryDto.emsNo!= ''">
                AND pts_ems_aexg.EMS_NO = #{emsQueryDto.emsNo}
            </if>
            <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                AND pts_ems_aexg.G_NO = #{emsQueryDto.gNo}
            </if>
            <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                AND pts_ems_aexg.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
            </if>
            <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                AND pts_ems_aexg.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
            </if>
        </where>
        ORDER BY pts_ems_aexg.G_NO DESC
    </select>
    <select id="listAexgList" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
            *
        FROM
            `pts_ems_aexg` a
        LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
        WHERE
            b.EMS_NO = #{emsNo}
            AND a.G_NO IN
        <foreach collection="gNoList" item="gNo" index="index"
                 open="(" close=")" separator=",">
            #{gNo}
        </foreach>
    </select>
    <select id="getOneByCond" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
            a.*
        FROM
            pts_ems_aexg a
                LEFT JOIN pts_ems_head b ON a.EMS_ID = b.ID
        WHERE
            a.EMS_NO = #{emsNo}
          AND a.COP_GNO = #{copGno}
          AND b.TENANT_ID = #{tenantId}
            LIMIT 1
    </select>
    <select id="listEmsAexgByReport" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
            emsNo,
            gNo,
            copGno,
            gName,
            codet,
            gModel,
            qty,
            zjcksl,
            ( qty - zjcksl ) AS scyl,
            ROUND(( qty - zjcksl ) / qty * 100, 4 ) AS ylbl,
            sjgjzcksl,
            cpthcksl,
            cpthjksl,
            ( zjcksl + sjgjzcksl + cpthcksl - cpthjksl ) AS sjcksl
        FROM
            (
                SELECT
                    ANY_VALUE ( pea.EMS_NO ) emsNo,
                    ANY_VALUE ( pea.G_NO ) gNo,
                    ANY_VALUE ( pea.COP_GNO ) copGno,
                    ANY_VALUE ( pea.G_NAME ) gName,
                    ANY_VALUE ( pea.CODET ) codet,
                    ANY_VALUE ( pea.G_MODEL ) gModel,
                    ANY_VALUE ( pea.QTY ) qty,
                    ANY_VALUE (
                    IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ), 0)) zjcksl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END )) sjgjzcksl,
                    ANY_VALUE (
                            SUM( CASE WHEN nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' THEN nl.DCL_QTY ELSE 0 END )) cpthcksl,
                    ANY_VALUE (
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'I'
                                                AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            )) cpthjksl
                FROM
                    pts_ems_aexg pea
                        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
                        AND ( nh.VRFDED_MARKCD = '2')
                        AND nh.PUTREC_NO = #{emsQueryDto.emsNo}
                        AND nh.TENANT_ID =  #{emsQueryDto.tenantId}
                        AND ( nh.IMPEXP_MARKCD = 'E' OR ( nh.IMPEXP_MARKCD = 'I' AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )) )
                        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
                        AND nl.PUTREC_SEQNO = pea.G_NO
                WHERE
                    pea.EMS_NO = #{emsQueryDto.emsNo}
                <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
                    AND pea.G_NO = #{emsQueryDto.gNo}
                </if>
                <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                    AND pea.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
                </if>
                <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                    AND pea.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
                </if>
                GROUP BY
                    pea.G_NO
                ORDER BY
                    pea.G_NO
            ) subquery


    </select>

    <select id="listEmsAexgByReportC" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
        gNo,
        qty,
        zjcksl,
        ( qty - zjcksl ) AS scyl,
        ROUND(( qty - zjcksl ) / qty * 100, 4 ) AS ylbl,
        sjgjzcksl,
        cpthcksl,
        cpthjksl,
        ( zjcksl + sjgjzcksl + cpthcksl - cpthjksl ) AS sjcksl
        FROM
        (
        SELECT
        ANY_VALUE ( pea.G_NO ) gNo,
        ANY_VALUE ( pea.QTY ) qty,
        ANY_VALUE (
        IFNULL(SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ), 0)) zjcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END )) sjgjzcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' THEN nl.DCL_QTY ELSE 0 END )) cpthcksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )) THEN
        nl.DCL_QTY ELSE 0
        END
        )) cpthjksl
        FROM
        pts_ems_aexg pea
        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
        AND ( nh.VRFDED_MARKCD = '2')
        AND nh.PUTREC_NO = #{emsNo}
        AND nh.TENANT_ID =  #{tenantId}
        AND ( nh.IMPEXP_MARKCD = 'E' OR ( nh.IMPEXP_MARKCD = 'I' AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )) )
        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
        AND nl.PUTREC_SEQNO = pea.G_NO
        WHERE
        pea.EMS_NO = #{emsNo}
        AND pea.G_NO IN
        <foreach collection="gNoList" item="gNo" index="index"
                 open="(" close=")" separator=",">
            #{gNo}
        </foreach>

        GROUP BY
        pea.G_NO
        ORDER BY
        pea.G_NO
        ) subquery


    </select>

    <select id="listEmsDetailWeightStatisticsAexg" resultType="org.jeecg.modules.business.vo.PtsEmsAexgWeightStatisticsVO">
        SELECT
            zjckWeight,
            sjgjzckWeight,
            cpthckWeight,
            cpthjkWeight,
            ( zjckWeight + sjgjzckWeight + cpthckWeight - cpthjkWeight ) ckbscpWeightSum
        FROM
            (
                SELECT
                    IFNULL(
                            SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ),
                            0
                    ) zjckWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_QTY ELSE 0 END ), 0 ) sjgjzckWeight,
                    IFNULL( SUM( CASE WHEN nh.SUPV_MODECD = '4600' OR nh.SUPV_MODECD = '4400' THEN nl.DCL_QTY ELSE 0 END ), 0 ) cpthckWeight,
                    IFNULL(
                            SUM(
                                    CASE

                                        WHEN (
                                            nh.IMPEXP_MARKCD = 'I'
                                                AND ( nh.SUPV_MODECD = '4600' OR nh.SUPV_MODECD = '4400' )) THEN
                                            nl.DCL_QTY ELSE 0
                                        END
                            ),
                            0
                    ) cpthjkWeight
                FROM
                    nems_invt_head nh
                        LEFT JOIN nems_invt_list nl ON nl.INV_ID = nh.ID
                        AND ( nh.VRFDED_MARKCD = '2')
                        AND nh.PUTREC_NO = #{emsNo}
                        AND nh.TENANT_ID = #{tenantId}
                        AND nh.IMPEXP_MARKCD = 'E'
            ) subquery



    </select>

    <select id="listEmsDetailAexgAmountByReport" resultType="org.jeecg.modules.business.entity.PtsEmsAexg">
        SELECT
        emsNo,
        gNo,
        copGno,
        gName,
        codet,
        gModel,
        qty,
        unit,
        decPrice,
        curr,
        zjckslQty,
        zjcksl,
        sjgjzcksl,
        cpthcksl,
        cpthjksl,
        ( zjcksl + sjgjzcksl + cpthcksl - cpthjksl ) sjckjehj
        FROM
        (
        SELECT
        ANY_VALUE ( nh.PUTREC_NO ) emsNo,
        ANY_VALUE ( pea.G_NO ) gNo,
        ANY_VALUE ( pea.COP_GNO ) copGno,
        ANY_VALUE ( pea.G_NAME ) gName,
        ANY_VALUE ( pea.CODET ) codet,
        ANY_VALUE ( pea.G_MODEL ) gModel,
        ANY_VALUE ( pea.QTY ) qty,
        ANY_VALUE ( pea.UNIT ) unit,
        ANY_VALUE ( pea.DEC_PRICE ) decPrice,
        ANY_VALUE ( nl.DCL_CURRCD ) curr,
        ANY_VALUE (
        IFNULL(
        SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_QTY ELSE 0 END ),
        0
        )) zjckslQty,
        ANY_VALUE (
        IFNULL(
        SUM( CASE WHEN nh.SUPV_MODECD = '0214' OR nh.SUPV_MODECD = '0615' OR nh.SUPV_MODECD = '0715' THEN nl.DCL_TOTALAMT ELSE 0 END ),
        0
        )) zjcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '0255' OR nh.SUPV_MODECD = '0654' THEN nl.DCL_TOTALAMT ELSE 0 END )) sjgjzcksl,
        ANY_VALUE (
        SUM( CASE WHEN nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' THEN nl.DCL_TOTALAMT ELSE 0 END )) cpthcksl,
        ANY_VALUE (
        SUM(
        CASE

        WHEN (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )) THEN
        nl.DCL_TOTALAMT ELSE 0
        END
        )) cpthjksl
        FROM
        pts_ems_aexg pea
        LEFT JOIN nems_invt_head nh ON pea.EMS_NO = nh.PUTREC_NO
        AND ( nh.VRFDED_MARKCD = '2')
        AND nh.PUTREC_NO =  #{emsQueryDto.emsNo}
        AND nh.TENANT_ID =  #{emsQueryDto.tenantId}
        AND (
        nh.IMPEXP_MARKCD = 'E'
        OR (
        nh.IMPEXP_MARKCD = 'I'
        AND ( nh.SUPV_MODECD = '4400' OR nh.SUPV_MODECD = '4600' )))
        LEFT JOIN nems_invt_list nl ON nh.ID = nl.INV_ID
        AND nl.PUTREC_SEQNO = pea.G_NO
        WHERE
        pea.EMS_NO =  #{emsQueryDto.emsNo}
          <if test="emsQueryDto.gNo != null and emsQueryDto.gNo != ''">
            AND pea.G_NO = #{emsQueryDto.gNo}
        </if>
         <if test="emsQueryDto.copGno != null and emsQueryDto.copGno != ''">
                           AND pea.COP_GNO LIKE CONCAT('%', #{emsQueryDto.copGno}, '%')
                      </if>
        <if test="emsQueryDto.gName != null and emsQueryDto.gName != ''">
                         AND pea.G_NAME LIKE CONCAT('%', #{emsQueryDto.gName}, '%')
         </if>

        GROUP BY
        pea.G_NO,
        nl.DCL_CURRCD
        ORDER BY
        pea.G_NO
        ) subquery


    </select>
    <select id="listInvtList2" resultType="org.jeecg.modules.business.entity.NemsInvtList">
        SELECT
            *
        FROM
            `nems_invt_list` b
                LEFT JOIN nems_invt_head a ON a.ID = b.INV_ID
        WHERE
          a.IMPEXP_MARKCD = 'E'
          AND a.VRFDED_MARKCD = '2'
          AND a.PUTREC_NO = #{emsNo}
          AND CONCAT(b.PUTREC_SEQNO, '|', b.GDS_MTNO, '|', b.HSCODE) = #{key}
    </select>
</mapper>
