<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StoreStocksMapper">

    <update id="updateQtySafe" parameterType="org.jeecg.modules.business.entity.StorageDetail">
        UPDATE `store_stocks`
        <set>
            <if test="ieFlag == 'I'.toString()">
                BEGIN_QTY = BEGIN_QTY + #{changeQty},
            </if>
            <if test="ieFlag == 'E'.toString()">
                BEGIN_QTY = BEGIN_QTY - #{changeQty},
                OCCUPY_QTY = OCCUPY_QTY - #{occupyQty},
            </if>
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate},
            BILL_NO = #{billNo},
            BOND_INVT_NO = #{bondInvtNo},
            HSCODE = #{hscode}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
            <if test="ieFlag == 'E'.toString()">
                AND BEGIN_QTY >= #{changeQty}
                AND OCCUPY_QTY >= #{occupyQty}
            </if>
        </where>
    </update>

    <update id="updateStockQtySafe" parameterType="org.jeecg.modules.business.entity.StorageDetail">
        UPDATE `store_stocks`
        <set>
            BEGIN_QTY = BEGIN_QTY - #{changeQty},
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
            AND BEGIN_QTY >= #{changeQty}
        </where>
    </update>

    <update id="updateOccQtySafe">
        UPDATE `store_stocks`
        <set>
            OCCUPY_QTY = OCCUPY_QTY - #{changeQty},
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
            AND OCCUPY_QTY >= #{changeQty}
        </where>
    </update>

    <insert id="addOccQtySafe">
        UPDATE `store_stocks`
        <set>
            OCCUPY_QTY = OCCUPY_QTY + #{changeQty},
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
            AND BEGIN_QTY >= (OCCUPY_QTY + #{changeQty})
        </where>
    </insert>

    <insert id="addOccQtySafeOneToMany">
        UPDATE `store_stocks`
        <set>
            OCCUPY_QTY = OCCUPY_QTY + #{changeQty},
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        WHERE
            ID = #{stockId}
        AND TENANT_ID = #{tenantId}
        AND BEGIN_QTY >= (OCCUPY_QTY + #{changeQty})
    </insert>

    <insert id="addOccAndStockQtySafe">
        UPDATE `store_stocks`
        <set>
            BEGIN_QTY = BEGIN_QTY + #{changeQty},
            OCCUPY_QTY = OCCUPY_QTY + #{occupyQty},
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
        </where>
    </insert>

    <update id="updateQtySafeRectify" parameterType="org.jeecg.modules.business.entity.StorageDetail">
        UPDATE `store_stocks`
        <set>
            <if test="ieFlag == 'I'.toString()">
                BEGIN_QTY = BEGIN_QTY + #{changeQty},
            </if>
            <if test="ieFlag == 'E'.toString()">
                BEGIN_QTY = BEGIN_QTY - #{changeQty},
                OCCUPY_QTY = OCCUPY_QTY - #{occupyQty},
            </if>
            VERSION = VERSION + 1,
            UPDATE_BY = #{updateBy},
            UPDATE_DATE = #{updateDate}
        </set>
        <where>
            STORE_CODE = #{storeCode}
            AND CUSTOMER = #{customer}
            AND COP_GNO = #{copGno}
            AND ITEM_NUMBER = #{itemNumber}
            AND TENANT_ID = #{tenantId}
            <choose>
                <when test="batchNo != null and batchNo != ''">
                    AND BATCH_NO = #{batchNo}
                </when>
                <otherwise>
                    AND (BATCH_NO IS NULL OR BATCH_NO = '')
                </otherwise>
            </choose>
            <choose>
                <when test="spaceName != null and spaceName != ''">
                    AND SPACE_NAME = #{spaceName}
                </when>
                <otherwise>
                    AND (SPACE_NAME IS NULL OR SPACE_NAME = '')
                </otherwise>
            </choose>
            <if test="ieFlag == 'I'.toString()">
                AND BEGIN_QTY >= #{changeQty}
                AND (BEGIN_QTY + #{changeQty}) >= 0
                AND VERSION = #{version}
            </if>
            <if test="ieFlag == 'E'.toString()">
                AND VERSION = #{version}
            </if>
        </where>
    </update>

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.StoreStocks">
        SELECT
            a.*,
        ( CASE WHEN d.DCL_UNITCD = '035' THEN d.DCL_QTY WHEN d.LAWF_UNITCD = '035' THEN d.LAWF_QTY ELSE d.SECD_LAWF_QTY END ) AS weight,
        (COALESCE(a.BEGIN_QTY, 0) - COALESCE(a.OCCUPY_QTY, 0)) AS availableQty,
            b.STORE_NAME
        FROM
            `store_stocks` a
        LEFT JOIN store_info b ON a.STORE_CODE = b.STORE_CODE
        LEFT JOIN nems_invt_head c ON a.BOND_INVT_NO = c.BOND_INVT_NO
        LEFT JOIN nems_invt_list d ON d.INV_ID = c.ID
        AND d.PUTREC_SEQNO = a.ITEM_NUMBER
        <where>
            a.TENANT_ID = #{storeStocksDTO.tenantId}
            <if test="storeStocksDTO.createDateSatrt != null and storeStocksDTO.createDateSatrt !=''
             and storeStocksDTO.createDateLast != null and storeStocksDTO.createDateLast !='' ">
                AND date_format(a.CREATE_DATE, '%Y-%m-%d')  between date_format(#{storeStocksDTO.createDateSatrt}, '%Y-%m-%d')
                and date_format(#{storeStocksDTO.createDateLast}, '%Y-%m-%d')
            </if>
            <if test="storeStocksDTO.storeCode != null and storeStocksDTO.storeCode != ''">
                AND a.STORE_CODE = #{storeStocksDTO.storeCode}
            </if>
            <if test="storeStocksDTO.areaCode != null and storeStocksDTO.areaCode != ''">
                AND a.AREA_NAME = #{storeStocksDTO.areaCode}
            </if>
            <if test="storeStocksDTO.detailType != null and storeStocksDTO.detailType != ''">
                AND a.DETAIL_TYPE = #{storeStocksDTO.detailType}
            </if>
            <if test="storeStocksDTO.pn != null and storeStocksDTO.pn != ''">
                AND a.PN LIKE CONCAT('%', #{storeStocksDTO.pn}, '%')
            </if>
            <if test="storeStocksDTO.copGno != null and storeStocksDTO.copGno != ''">
                AND a.COP_GNO = #{storeStocksDTO.copGno}
            </if>
            <if test="storeStocksDTO.batchNo != null and storeStocksDTO.batchNo != ''">
                AND a.BATCH_NO LIKE CONCAT('%', #{storeStocksDTO.batchNo}, '%')
            </if>
            <if test="storeStocksDTO.customer != null and storeStocksDTO.customer != ''">
                AND a.CUSTOMER = #{storeStocksDTO.customer}
            </if>
            <if test="storeStocksDTO.itemNumber != null and storeStocksDTO.itemNumber != ''">
                AND a.ITEM_NUMBER = #{storeStocksDTO.itemNumber}
            </if>
            <if test="storeStocksDTO.billNo != null and storeStocksDTO.billNo != ''">
                AND a.BILL_NO LIKE CONCAT('%', #{storeStocksDTO.billNo}, '%')
            </if>
            <if test="storeStocksDTO.bondInvtNo != null and storeStocksDTO.bondInvtNo != ''">
                AND a.BOND_INVT_NO LIKE CONCAT('%', #{storeStocksDTO.bondInvtNo}, '%')
            </if>
            <if test="storeStocksDTO.hscode != null and storeStocksDTO.hscode != ''">
                AND a.HSCODE LIKE CONCAT('%', #{storeStocksDTO.hscode}, '%')
            </if>
            <if test="storeStocksDTO.hasBeginQty == true ">
                AND a.BEGIN_QTY &gt; 0
            </if>
            <if test="storeStocksDTO.spaceCode != null and storeStocksDTO.spaceCode != ''">
                AND a.SPACE_CODE LIKE CONCAT('%', #{storeStocksDTO.spaceCode}, '%')
            </if>

        </where>
        ORDER BY
            a.CREATE_DATE DESC,
            a.STORE_CODE
    </select>
    <select id="queryPageList_" resultType="org.jeecg.modules.business.entity.StoreStocks">
        SELECT
            a.*,
            b.STORE_NAME
        FROM
        `store_stocks` a
        LEFT JOIN store_info b ON a.STORE_CODE = b.STORE_CODE
        <where>
            a.TENANT_ID = #{storeStocksDTO.tenantId}
            <if test="storeStocksDTO.createDateSatrt != null and storeStocksDTO.createDateSatrt !=''
             and storeStocksDTO.createDateLast != null and storeStocksDTO.createDateLast !='' ">
                AND date_format(a.CREATE_DATE, '%Y-%m-%d')  between date_format(#{storeStocksDTO.createDateSatrt}, '%Y-%m-%d')
                and date_format(#{storeStocksDTO.createDateLast}, '%Y-%m-%d')
            </if>
            <if test="storeStocksDTO.storeCode != null and storeStocksDTO.storeCode != ''">
                AND a.STORE_CODE = #{storeStocksDTO.storeCode}
            </if>
            <if test="storeStocksDTO.areaCode != null and storeStocksDTO.areaCode != ''">
                AND a.AREA_NAME = #{storeStocksDTO.areaCode}
            </if>
            <if test="storeStocksDTO.detailType != null and storeStocksDTO.detailType != ''">
                AND a.DETAIL_TYPE = #{storeStocksDTO.detailType}
            </if>
            <if test="storeStocksDTO.pn != null and storeStocksDTO.pn != ''">
                AND a.PN LIKE CONCAT('%', #{storeStocksDTO.pn}, '%')
            </if>
            <if test="storeStocksDTO.copGno != null and storeStocksDTO.copGno != ''">
                AND a.COP_GNO = #{storeStocksDTO.copGno}
            </if>
            <if test="storeStocksDTO.batchNo != null and storeStocksDTO.batchNo != ''">
                AND a.BATCH_NO LIKE CONCAT('%', #{storeStocksDTO.batchNo}, '%')
            </if>
            <if test="storeStocksDTO.customer != null and storeStocksDTO.customer != ''">
                AND a.CUSTOMER = #{storeStocksDTO.customer}
            </if>
            <if test="storeStocksDTO.itemNumber != null and storeStocksDTO.itemNumber != ''">
                AND a.ITEM_NUMBER = #{storeStocksDTO.itemNumber}
            </if>
            <if test="storeStocksDTO.billNo != null and storeStocksDTO.billNo != ''">
                AND a.BILL_NO LIKE CONCAT('%', #{storeStocksDTO.billNo}, '%')
            </if>
            <if test="storeStocksDTO.bondInvtNo != null and storeStocksDTO.bondInvtNo != ''">
                AND a.BOND_INVT_NO LIKE CONCAT('%', #{storeStocksDTO.bondInvtNo}, '%')
            </if>
            <if test="storeStocksDTO.hscode != null and storeStocksDTO.hscode != ''">
                AND a.HSCODE LIKE CONCAT('%', #{storeStocksDTO.hscode}, '%')
            </if>
            <if test="storeStocksDTO.hasBeginQty == true ">
                AND a.BEGIN_QTY &gt; 0
            </if>
            <if test="storeStocksDTO.spaceCode != null and storeStocksDTO.spaceCode != ''">
                AND a.SPACE_CODE LIKE CONCAT('%', #{storeStocksDTO.spaceCode}, '%')
            </if>
        </where>
        ORDER BY
            a.CREATE_DATE DESC,
            a.STORE_CODE
    </select>
</mapper>
