<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StorageRepairOrderDetailMapper">

    <select id="getRepairOrderDetailByCond"
            resultType="org.jeecg.modules.business.entity.StorageRepairOrderDetail">
        SELECT
            *
        FROM
            `storage_repair_order_detail`
        WHERE
            CONCAT( COP_GNO, '|', PN, '|', DETAIL_TYPE ) = #{searchCondition}
    </select>
    <select id="getRepairOrderByCond" resultType="org.jeecg.modules.business.entity.StorageRepairOrderDetail">
        SELECT
            b.*
        FROM
            `storage_repair_order` a
                LEFT JOIN storage_repair_order_detail b ON a.ID = b.HEAD_ID
        WHERE
            CONCAT( b.COP_GNO, '|', b.PN, '|', b.DETAIL_TYPE ) = #{searchCondition}
          AND a.`STATUS` = #{status}
    </select>

    <select id="getRepairOrderDetailByCond_"
            resultType="org.jeecg.modules.business.entity.StorageRepairOrderDetail">
        SELECT
            *
        FROM
            `storage_repair_order_detail`
        WHERE
            CONCAT( COP_GNO, '|', DETAIL_TYPE ) = #{searchCondition}
        AND TENANT_ID = #{tenantId}
    </select>
    <select id="getRepairOrderByCond_" resultType="org.jeecg.modules.business.entity.StorageRepairOrderDetail">
        SELECT
            b.*
        FROM
            `storage_repair_order` a
                LEFT JOIN storage_repair_order_detail b ON a.ID = b.HEAD_ID
        WHERE
            CONCAT( b.COP_GNO, '|', b.DETAIL_TYPE ) = #{searchCondition}
          AND a.`STATUS` = #{status}
          AND a.TENANT_ID = #{tenantId}
    </select>
    <select id="getRepairOrderDetailByCond__"
            resultType="org.jeecg.modules.business.entity.StorageRepairOrderDetail">
        SELECT
            b.*
        FROM
            storage_repair_order_detail b
                LEFT JOIN `storage_repair_order` a ON a.ID = b.HEAD_ID
        WHERE
            CONCAT( b.COP_GNO, '|', b.DETAIL_TYPE ) = #{searchCondition}
          AND a.REPAIR_NO IN
            <foreach collection="relRepairNosList" item="item" open="(" close=")" separator=",">
                (#{item})
            </foreach>
          AND a.TENANT_ID = #{tenantId}
    </select>
</mapper>
