<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.RecShipPlanIMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.RecShipPlanI">
        SELECT
            a.*
        FROM
            `rec_ship_plan_i` a
        <where>
            <if test="recShipPlanI.cJkywcm != null and recShipPlanI.cJkywcm != ''">
                AND a.C_JKYWCM = #{recShipPlanI.cJkywcm}
            </if>
            <if test="recShipPlanI.cJkhc != null and recShipPlanI.cJkhc != ''">
                AND a.C_JKHC = #{recShipPlanI.cJkhc}
            </if>
        </where>
        ORDER BY
        a.CREATE_DATE DESC
    </select>
</mapper>
