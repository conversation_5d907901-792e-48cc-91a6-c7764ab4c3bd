<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StoreSpaceMapper">

    <select id="getSpaceByCond" resultType="org.jeecg.modules.business.entity.StoreSpace">
        SELECT
            ss.*,
            sa.AREA_NAME
        FROM
            `store_space` ss
                LEFT JOIN store_area sa ON ss.AREA_CODE = sa.AREA_CODE
        WHERE
            sa.`TYPE` = #{type}
          AND sa.CUSTOMER = #{customer}
        LIMIT 1
    </select>
</mapper>
