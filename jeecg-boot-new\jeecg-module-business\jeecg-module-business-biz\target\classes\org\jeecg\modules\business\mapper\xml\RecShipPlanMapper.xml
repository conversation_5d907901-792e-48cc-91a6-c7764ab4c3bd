<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.RecShipPlanMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.RecShipPlan">
        SELECT
            a.*
        FROM
            `rec_ship_plan` a
        <where>
            <if test="recShipPlan.cCkywcm != null and recShipPlan.cCkywcm != ''">
                AND a.C_CKYWCM = #{recShipPlan.cCkywcm}
            </if>
            <if test="recShipPlan.cCkhc != null and recShipPlan.cCkhc != ''">
                AND a.C_CKHC = #{recShipPlan.cCkhc}
            </if>
        </where>
        ORDER BY
        a.CREATE_DATE DESC
    </select>
</mapper>
