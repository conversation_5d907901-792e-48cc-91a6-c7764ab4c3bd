<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.TradeInfoMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.TradeInfo">
        SELECT
        trade_info.*,
        grouping_info.GROUPING_NO,
        grouping_info.GROUPING_STATUS,
        grouping_info.GROUPING_DATE,
        packing_info.PACKING_NO,
        packing_info.PACKING_STATUS,
        packing_info.PACKING_DATE
        FROM
        trade_info left join
        grouping_info on trade_info.ID=grouping_info.TRADE_INFO_ID
        left join packing_info on
        trade_info.ID=packing_info.TRADE_INFO_ID
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ORDER BY trade_info.CREATE_TIME DESC
    </select>


</mapper>
