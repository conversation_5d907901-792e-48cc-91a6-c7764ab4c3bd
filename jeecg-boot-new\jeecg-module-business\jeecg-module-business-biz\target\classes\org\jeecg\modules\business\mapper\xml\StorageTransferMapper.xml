<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StorageTransferMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.StorageTransfer">
        SELECT
            a.*,
            MAX( b.BATCH_NO ) batchNo
        FROM
            `storage_transfer` a
                LEFT JOIN storage_transfer_detail b ON a.TRANSFER_NO = b.TRANSFER_NO
        <where>
            a.TENANT_ID = #{storageTransfer.tenantId}
            <if test="storageTransfer.transferNo != null and storageTransfer.transferNo != ''">
                AND a.TRANSFER_NO LIKE CONCAT('%', #{storageTransfer.transferNo}, '%')
            </if>
            <if test="storageTransfer.areaCodeBefore != null and storageTransfer.areaCodeBefore != ''">
                AND a.AREA_CODE_BEFORE = #{storageTransfer.areaCodeBefore}
            </if>
            <if test="storageTransfer.areaCodeAfter != null and storageTransfer.areaCodeAfter != ''">
                AND a.AREA_CODE_AFTER = #{storageTransfer.areaCodeAfter}
            </if>
            <if test="storageTransfer.status != null and storageTransfer.status != ''">
                AND a.STATUS = #{storageTransfer.status}
            </if>
            <if test="storageTransfer.customer != null and storageTransfer.customer != ''">
                AND a.CUSTOMER = #{storageTransfer.customer}
            </if>
            <if test="storageTransfer.storeCode != null and storageTransfer.storeCode != ''">
                AND a.STORE_CODE = #{storageTransfer.storeCode}
            </if>
            <if test="storageTransfer.appDate != null">
                AND date_format(a.APP_DATE, '%Y-%m-%d') = date_format(#{storageTransfer.appDate}, '%Y-%m-%d')
            </if>
            <if test="storageTransfer.batchNo != null and storageTransfer.batchNo != ''">
                AND b.BATCH_NO LIKE CONCAT('%', #{storageTransfer.batchNo}, '%')
            </if>
            <if test="storageTransfer.relRepairNo != null and storageTransfer.relRepairNo != ''">
                AND a.REL_REPAIR_NO LIKE CONCAT('%', #{storageTransfer.relRepairNo}, '%')
            </if>
        </where>
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>

    <select id="getTransferDetailByRepair"
            resultType="org.jeecg.modules.business.entity.StorageTransferDetail">
        SELECT
            std.*
        FROM
            `storage_transfer_detail` std
                LEFT JOIN storage_transfer st ON std.TRANSFER_NO = st.TRANSFER_NO
        WHERE
            st.REL_REPAIR_NO = #{repairNo}
          AND st.STEP = '3'
    </select>
</mapper>
