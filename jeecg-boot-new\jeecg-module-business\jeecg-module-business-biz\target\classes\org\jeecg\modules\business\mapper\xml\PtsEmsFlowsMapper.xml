<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PtsEmsFlowsMapper">

    <select id="listFlowsByEmsNoAndInvtIds" resultType="org.jeecg.modules.business.entity.PtsEmsDetail">
        SELECT
            d.*,
            f.INVT_NO invtNo,
            CASE
            d.GOODS_TYPE
            WHEN 1 THEN
            '料件'
            ELSE '成品'
            END goodsTypeStr,
            CASE
            f.OP_STATUS
            WHEN 0 THEN
            '操作失败'
            WHEN 1 THEN
            '操作成功'
            WHEN - 1 THEN
            '已冲正'
            WHEN - 2 THEN
            '删撤单'
            END opStatusStr,
            f.OP_USER opUser
        FROM
            `PTS_EMS_DETAIL` d
        LEFT JOIN PTS_EMS_FLOWS f ON d.FLOW_ID = f.ID
        WHERE
            d.EMS_NO = #{emsNo}
        <if test="invtList != null and invtList.size > 0">
            AND f.INVT_NO IN
            <foreach collection="invtList" item="invtNo" index="i" open="(" close=")" separator=",">
                #{invtNo}
            </foreach>
        </if>
        <if test="goodsType != null and goodsType != ''">
            AND d.GOODS_TYPE = #{goodsType}
        </if>
        ORDER BY
        d.OP_TIME DESC
    </select>
    <select id="listFlowsByEmsNoAndGNos" resultType="org.jeecg.modules.business.entity.PtsEmsDetail">
        SELECT
            d.*,
            f.INVT_NO invtNo,
            CASE
            d.GOODS_TYPE
            WHEN 1 THEN
            '料件'
            ELSE '成品'
            END goodsTypeStr,
            CASE
            f.OP_STATUS
            WHEN 0 THEN
            '操作失败'
            WHEN 1 THEN
            '操作成功'
            WHEN - 1 THEN
            '已冲正'
            WHEN - 2 THEN
            '删撤单'
            END opStatusStr,
            f.OP_USER opUser
        FROM
            `PTS_EMS_DETAIL` d
        LEFT JOIN PTS_EMS_FLOWS f ON d.FLOW_ID = f.ID
        WHERE
            d.EMS_NO = #{emsNo}
        <if test="gNoList != null and gNoList.size > 0">
            AND d.G_NO IN
            <foreach collection="gNoList" item="gNo" index="i" open="(" close=")" separator=",">
                #{gNo}
            </foreach>
        </if>
        <if test="goodsType != null and goodsType != ''">
            AND d.GOODS_TYPE = #{goodsType}
        </if>
        ORDER BY
        d.OP_TIME DESC
    </select>
</mapper>
