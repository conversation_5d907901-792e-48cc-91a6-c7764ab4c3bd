<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.RepairCancellationOrdersMapper">

    <select id="getChartRepairCancellationOrders" resultType="org.jeecg.modules.business.entity.RepairCancellationOrders">
        select IFNULL( count (*),0 ) count,DATE_FORMAT(UPDATE_TIME, '%Y-%m-%d') updateTimeEnd
        from repair_cancellation_orders
        WHERE DATE_FORMAT(UPDATE_TIME, '%Y-%m-%d') IN
        <foreach collection="lastWeekDays" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by DATE_FORMAT(UPDATE_TIME, '%Y-%m-%d')
    </select>
</mapper>
