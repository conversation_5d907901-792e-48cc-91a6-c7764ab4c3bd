<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.jeecg.modules.business.mapper.LogisticsTrackingInfoMapper">
    <select id="selectByPage" resultType="org.jeecg.modules.business.entity.LogisticsTrackingInfo">
        select * from user ${ew.customSqlSegment}
    </select>
    <select id="listLogisticsTrackingNodeByInFoId" resultType="org.jeecg.modules.business.entity.LogisticsTrackingNode">
        select LOGISTICS_TRACKING_NODE.*,LOGISTICS_TRACKING_NODE_STATUS.ID statusId,
               LOGISTICS_TRACKING_NODE_STATUS.STATUS_NAME statusName
               from LOGISTICS_TRACKING_NODE left join LOGISTICS_TRACKING_NODE_STATUS
        on LOGISTICS_TRACKING_NODE.LOGISTICS_TRACKING_NODE_STATUS=LOGISTICS_TRACKING_NODE_STATUS.ID
    </select>


</mapper>
