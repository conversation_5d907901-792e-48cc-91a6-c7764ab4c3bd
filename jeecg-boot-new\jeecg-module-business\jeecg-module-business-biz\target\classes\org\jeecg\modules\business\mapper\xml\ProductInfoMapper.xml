<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ProductInfoMapper">

    <select id="listTradeInfo" parameterType="java.lang.String" resultType="org.jeecg.modules.business.entity.ProductInfo">
        select
        hs.hscode as customsCodeInfoCode,
        hs.hsname ,
        hs.addtaxrate addedTaxRate,
        hs.backtaxrate taxRebateRate,
        hs.qtyunit legalUnitCode,
        hs.qtcunit secondUnitCode,
        un.name as legalUnit,
        twUn.name as secondUnit,
        concat_ws(':',hs.hscode,hs.hsname) customsCodeInfoId,
        GROUP_CONCAT(concat_ws(':',er.no,er.element) SEPARATOR '\n') customsDeclarationElements
        from erp_hscodes hs
        left join erp_report_elements er
        on hs.hscode = er.hscode
        left join erp_units un
        on hs.qtyunit = un.code
        left join erp_units twUn
        on hs.qtcunit = twUn.code
        <where>
            er.element not in (select ere.element from erp_report_elements ere where ere.element like '%GTIN%' or ere.element like '%CAS%' or ere.element like '%其他%')
            and hs.hscode = #{customsCodeInfoCode,jdbcType=VARCHAR}
        </where>

        GROUP BY hs.id
        ORDER BY hs.hscode asc

    </select>

    <select id="EnglishCode" parameterType="java.lang.String" resultType="java.lang.String">
        select e.code
        from erp_countries e
        <where>
            e.name in (
            select e.name from erp_countries e
            where
            e.code = #{numCode,jdbcType=VARCHAR}
            )
            and e.isenabled = '1'
        </where>
    </select>

    <select id="chnlishCode" parameterType="java.lang.String" resultType="java.lang.String">
        select e.code
        from erp_countries e
        <where>
            e.name in (
            select e.name from erp_countries e
            where
            e.name = #{chnCode,jdbcType=VARCHAR}
            )
            and e.isenabled = '1'
        </where>
    </select>
    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.ProductInfo">
        SELECT
            id,
            tenant_id,
            create_by,
            create_time,
            update_by,
            update_time,
            product_category_info_id,
            english_name,
            chinese_name,
            customs_code_info_id,
            customs_code_info_code,
            origin_country,
            is_disable,
            bar_code,
            english_describe,
            chinese_describe,
            product_remarks,
            product_picture,
            added_tax_rate,
            tax_rebate_rate,
            sales_guidance_price,
            customs_declaration_elements,
            product_specification_model,
            legal_unit,
            second_unit,
            legal_unit_code,
            second_unit_code,
            gross_weight,
            net_weight,
            volume,
            del_flag,
            remarks,
            product_tag_id,
            IE_FLAG,
            pn,
        NET_WEIGHT_FLUCTUATION_RATIO,
        PRICE,
        PRICE_FLUCTUATION_RATIO,
        TARIFFS_NAME,
        HSNAME,
        IAQCATEGORY,
        MONITORCONDITION,
        QUNIT,
        IS_CCC,IS_DUAL_USE_ITEM,IS_PROHIBIT,IS_EXAMINE
        FROM
            product_info
        <where>
            del_flag = #{productInfo.delFlag}
            AND tenant_id = #{productInfo.tenantId}
            <if test="productInfo.pn != null and productInfo.pn != ''">
                AND PN LIKE CONCAT('%', #{productInfo.pn}, '%')
            </if>
            <if test="productInfo.productCategoryInfoId != null and productInfo.productCategoryInfoId != ''">
                AND PRODUCT_CATEGORY_INFO_ID = #{productInfo.productCategoryInfoId}
            </if>
            <if test="productInfo.chineseName != null and productInfo.chineseName != ''">
                AND CHINESE_NAME LIKE CONCAT('%', #{productInfo.chineseName}, '%')
            </if>
            <if test="productInfo.englishName != null and productInfo.englishName != ''">
                AND ENGLISH_NAME LIKE CONCAT('%', #{productInfo.englishName}, '%')
            </if>
            <if test="productInfo.ieFlag != null and productInfo.ieFlag != ''">
                AND IE_FLAG = #{productInfo.ieFlag}
            </if>
            <if test="productInfo.isExamine == 1">
                AND IS_EXAMINE = '1'
            </if>
            <if test="productInfo.isExamine == 0">
                AND (IS_EXAMINE = '0' OR IS_EXAMINE IS NULL OR IS_EXAMINE = '')
            </if>
            <if test="productInfo.customsCodeInfoId != null and productInfo.customsCodeInfoId != ''">
                AND CUSTOMS_CODE_INFO_ID LIKE CONCAT('%', #{productInfo.customsCodeInfoId}, '%')
            </if>
            <if test="productInfo.productTagIdList != null and productInfo.productTagIdList.size() > 0">
                <foreach collection="productInfo.productTagIdList" item="productTagId" open="AND (" close=")" separator="or">
                    PRODUCT_TAG_ID LIKE CONCAT('%', #{productTagId}, '%')
                </foreach>
            </if>
        <if test="productInfo.type == 1">
        AND IS_EXAMINE = '1'
        AND IS_PROHIBIT != '1'
        </if>
        </where>
        ORDER BY
            create_time DESC
    </select>

</mapper>
