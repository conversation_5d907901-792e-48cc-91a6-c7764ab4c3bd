<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.RateInfoMapper">
    <select id="getRateList" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        SELECT
            *
        FROM
            rate_info
                    <where>
                        del_flag = 0
                        <if test="rateDateStart != null and rateDateStart != ''">
                            AND rate_date&gt;= CONCAT('','${rateDateStart}',' 00:00:00')
                        </if>
                        <if test="rateDateEnd != null and rateDateEnd != ''">
                            AND rate_date &lt;= CONCAT('','${rateDateEnd}',' 23:59:59')
                        </if>
                        <if test="tenantId != null and tenantId != ''">
                            and tenant_id = #{tenantId,jdbcType=INTEGER}
                        </if>
                        <if test="currencyCode != null and currencyCode != ''">
                            and CURRENCY = #{currencyCode}
                        </if>
                    </where>
        ORDER BY
            CURRENCY
    </select>

    <select id="getEveryRates" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        SELECT
            currency_code,
            currency,
            currency_name,
            rate,
            rate_date,
               rmb
        FROM rate_info
        <where>
            <if test="tenantId != null and tenantId != ''">
                and tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
            AND del_flag = 0
            AND currency = #{currency,jdbcType=VARCHAR}
            AND rate_date &gt; DATE_SUB(CURRENT_DATE, INTERVAL 10 DAY)
            AND rate_date &lt;= CURRENT_DATE
        </where>
        ORDER BY rate_date

    </select>

    <select id="getFirstRate" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        SELECT currency_code,
        currency,
        currency_name,
        rate,
        rate_date
        FROM rate_info
        WHERE rate_date IN
        (
        SELECT max(rate_date)
        FROM rate_info
        <where>
            del_flag = 0
            AND tenant_id = #{tenantId,jdbcType=INTEGER}
            AND currency_code = #{currencyCode,jdbcType=VARCHAR}
            AND DATE_FORMAT( #{rateDate,jdbcType=DATE}, '%Y-%m-%d' ) &gt;= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 10 MONTH ), '%Y-%m-%d' )
            AND DATE_FORMAT( rate_date, '%Y-%m-%d' ) &lt; DATE_FORMAT( #{rateDate,jdbcType=DATE}, '%Y-%m-%d' )
        </where>
        GROUP BY currency_code
        )
    </select>

    <select id="queryFilesInfo" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        select
        distinct r.currency_code
        from
        rate_info r
        <where>
            r.del_flag = 0
            <if test="tenantId != null">
                and r.tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryRateInfo" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        select
            currency_code,
            rate_date,
            rate
        from rate_info
        <where>
            rate_date IN
            (
            SELECT max(rate_date)
            FROM rate_info
            <where>
                del_flag = 0
                AND tenant_id = #{tenantId,jdbcType=INTEGER}
                    AND rate_date &gt;= CONCAT('','${rateDateStart}',' 00:00:00')
                    AND rate_date &lt;= CONCAT('','${rateDateEnd}',' 23:59:59')
            </where>
            GROUP BY currency_code
            )
            <if test="currencyCode != null and currencyCode != ''">
                and currency_code = #{currencyCode}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryRateDateInfo" parameterType="org.jeecg.modules.business.entity.RateInfo"
            resultType="org.jeecg.modules.business.entity.RateInfo">
        select
        r.rate_date,
        r.currency_code
        from rate_info r
        <where>
            r.del_flag = '0'
            <if test="tenantId != null">
                and r.tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
            <if test="rateDate != null">
                and r.rate_date = DATE_FORMAT(#{rateDate,jdbcType=DATE},'%Y-%m-%d')
            </if>
            <if test="currencyCode != null">
                and r.currency_code = #{currencyCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listExchangeRateByMonth" resultType="org.jeecg.modules.business.entity.RateInfo">
        SELECT
        rate_info.*
        FROM
        rate_info
        WHERE
         date_format(`RATE_DATE`,'%Y-%m' )  IN
        <foreach collection="monthList" item ="monthList" index="i" open="(" close=")" separator=",">
            #{monthList}
        </foreach>
        and RMB IS NOT NULL
        and USD IS NOT NULL
    </select>

</mapper>
