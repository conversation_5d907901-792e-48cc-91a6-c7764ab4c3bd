<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.CustomsExemptMapper">

    <select id="getCarrierByTradeTypeAndEmsType" resultType="org.jeecg.modules.business.entity.CustomsExempt">
        SELECT * FROM CUSTOMS_EXEMPT WHERE TRADE_TYPE = #{tradeType}
        <if test="emsType != null and emsType != ''">
            AND EMS_TYPE LIKE CONCAT('%',#{emsType},'%')
        </if>
        <if test="emsType == null or emsType == ''">
            AND (EMS_TYPE IS NULL OR EMS_TYPE ='')
        </if>
    </select>
</mapper>
