<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.MatchingInfoMapper">
<!--    查询订单外汇匹配信息-->
    <select id="queryOrderMatching" parameterType="org.jeecg.modules.business.entity.OrderMatchingInfoBiz"
            resultType="org.jeecg.modules.business.entity.OrderMatchingInfoBiz">
        SELECT
        m.id,
        m.tenant_id,
        m.create_by,
        m.create_time,
        m.update_by,
        m.update_time,
        m.order_info_id,
        m.collection_matching_amount,
        m.del_flag,
        m.foreign_exchange_collection_id,
        f.collection_date,
        m.matching_type,
        f.import_amount,
        f.rate
        FROM
        matching_info m
        LEFT JOIN foreign_exchange_collection_info f ON m.foreign_exchange_collection_id = f.id
        AND m.tenant_id = f.tenant_id
        AND f.del_flag = 0
        WHERE
        m.del_flag = 0
        <if test="orderMatchingInfo.orderInfoId != null">
            AND m.order_info_id = #{orderMatchingInfo.orderInfoId,jdbcType=VARCHAR}
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>