<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.JgVFlyHzqdHeadMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.JgVFlyHzqdHead">
        SELECT
            *
        FROM
            JG_V_Fly_Hzqd_Head
        WHERE
            bizopEtpsNm = #{customerName}
          AND IsDelete = 0
          AND CONVERT(DATETIME, invtDclTime, 120) >= #{starDate}
          AND CONVERT(DATETIME, invtDclTime, 120) &lt;= #{lastDate}
        ORDER BY
            invtDclTime DESC
    </select>
<!--    <select id="listHzqdData" resultType="org.jeecg.modules.business.entity.JgVFlyHzqdHead">-->
<!--        SELECT-->
<!--            *-->
<!--        FROM-->
<!--            JG_V_Fly_Hzqd_Head-->
<!--        WHERE-->
<!--            (bizopEtpsNm = #{bizopEtpsNm} OR bizopEtpsNm = '森峰（济南）进出口有限公司')-->
<!--          AND ( supvModecd = #{supvModecd} OR supvModecd = #{supvModecd1} )-->
<!--        AND IsDelete = 0-->
<!--        AND (putrecNo = 'H430622A0004' OR putrecNo = 'H430621A0001')-->
<!--        AND (-->
<!--        ( CONVERT ( DATETIME, invtDclTime, 120 ) >= #{startDate} AND CONVERT ( DATETIME, invtDclTime, 120 ) &lt;= #{lastDate} )-->
<!--        OR CAST ( UpdateDate AS DATE ) >= CAST ( DATEADD( DAY, - 3, GETDATE( ) ) AS DATE )-->
<!--        )-->
<!--        ORDER BY-->
<!--            invtDclTime DESC-->
<!--    </select>-->

    <select id="listHzqdData" resultType="org.jeecg.modules.business.entity.JgVFlyHzqdHead">
        SELECT
            *
        FROM
            JG_V_Fly_Hzqd_Head
        WHERE
            bizopEtpsNm = #{bizopEtpsNm}
            AND supvModecd IN
            <foreach item="supvModecd" index="index" collection="supvModecds" open="(" separator="," close=")">
                #{supvModecd}
            </foreach>
            AND putrecNo IN
            <foreach item="putrecNo" index="index" collection="putrecNos" open="(" separator="," close=")">
                #{putrecNo}
            </foreach>
          AND IsDelete = 0
        <if test="isAll == null or isAll == ''">
            AND (
            ( CONVERT ( DATETIME, invtDclTime, 120 ) >= #{startDate} AND CONVERT ( DATETIME, invtDclTime, 120 ) &lt;= #{lastDate} )
            OR CAST ( UpdateDate AS DATE ) >= CAST ( DATEADD( DAY, - 3, GETDATE( ) ) AS DATE )
            )
        </if>
        <if test="isAll == '2'.toString()">
            AND listStat != '0'
            AND CAST(UpdateDate AS DATE) >= CAST(#{startDate} AS DATE)
            AND CAST(UpdateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
        </if>
        ORDER BY
            invtDclTime DESC
    </select>

    <select id="listHzqdDataCommon" resultType="org.jeecg.modules.business.entity.JgVFlyHzqdHead">
        SELECT
            *
        FROM
            JG_V_Fly_Hzqd_Head
        WHERE
            bizopEtpsno IN
        <foreach item="bizopEtpsno" index="index" collection="bizopEtpsnos" open="(" separator="," close=")">
            #{bizopEtpsno}
        </foreach>
            AND IsDelete = 0
        <if test="isAll == null or isAll == ''">
            <if test="bondInvtNo != null and bondInvtNo != ''">
                AND bondInvtNo = #{bondInvtNo}
            </if>
            <if test="bondInvtNo == null or bondInvtNo == ''">
                AND CAST(UpdateDate AS DATE) >= CAST(#{startDate} AS DATE)
                AND CAST(UpdateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
            </if>
        </if>
        <if test="isAll == '2'.toString()">
            AND listStat != '0'
            AND CAST(UpdateDate AS DATE) >= CAST(#{startDate} AS DATE)
            AND CAST(UpdateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
        </if>
        <if test="isAll == '3'.toString()">
            AND listStat != '0'
        </if>
        ORDER BY
            UpdateDate DESC
    </select>
    <select id="countHzqdDataCommon" resultType="java.lang.Long">
        SELECT
           COUNT(1)
        FROM
            JG_V_Fly_Hzqd_Head
        WHERE
            bizopEtpsno IN
        <foreach item="bizopEtpsno" index="index" collection="bizopEtpsnos" open="(" separator="," close=")">
            #{bizopEtpsno}
        </foreach>
            AND IsDelete = 0
        <if test="isAll == null or isAll == ''">
            <if test="bondInvtNo != null and bondInvtNo != ''">
                AND bondInvtNo = #{bondInvtNo}
            </if>
            <if test="bondInvtNo == null or bondInvtNo == ''">
                AND CAST(UpdateDate AS DATE) >= CAST(#{startDate} AS DATE)
                AND CAST(UpdateDate AS DATE) &lt;= CAST(#{lastDate} AS DATE)
            </if>
        </if>
    </select>
</mapper>
