<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.EnterpriseCustomsRecordMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.EnterpriseCustomsRecord">
        SELECT
            *
        FROM
            `enterprise_customs_record`
        <where>
            <if test="enterpriseCustomsRecord.enterpriseName != null and enterpriseCustomsRecord.enterpriseName != ''">
                AND ENTERPRISE_NAME LIKE CONCAT('%', #{enterpriseCustomsRecord.enterpriseName}, '%')
            </if>
            <if test="enterpriseCustomsRecord.type != null and enterpriseCustomsRecord.type != ''">
                AND `TYPE` = #{enterpriseCustomsRecord.type}
            </if>
            <if test="enterpriseCustomsRecord.orgCoClassName != null and enterpriseCustomsRecord.orgCoClassName != ''">
                AND ORG_CO_CLASS_NAME = #{enterpriseCustomsRecord.orgCoClassName}
            </if>
            <if test="enterpriseCustomsRecord.newCoClassName != null and enterpriseCustomsRecord.newCoClassName != ''">
                AND NEW_CO_CLASS_NAME = #{enterpriseCustomsRecord.newCoClassName}
            </if>
            <if test="enterpriseCustomsRecord.etpsNm != null and enterpriseCustomsRecord.etpsNm != ''">
                AND ETPS_NM LIKE CONCAT('%', #{enterpriseCustomsRecord.etpsNm}, '%')
            </if>
            <if test="enterpriseCustomsRecord.caseNatureNm != null and enterpriseCustomsRecord.caseNatureNm != ''">
                AND CASE_NATURE_NM = #{enterpriseCustomsRecord.caseNatureNm}
            </if>
            <if test="enterpriseCustomsRecord.caseNo != null and enterpriseCustomsRecord.caseNo != ''">
                AND CASE_NO LIKE CONCAT('%', #{enterpriseCustomsRecord.caseNo}, '%')
            </if>
        </where>
        ORDER BY CREATE_DATE DESC
    </select>
</mapper>
