<template>
	<j-modal
		:footer="null"
		:keyboard="false"
		:maskClosable="false"
		:title="modalTitle"
		:visible="visible"
		:width="1400"
		cancelText="关闭"
		@cancel="handleCancel"
	>
		<div class="hscode-detail-container">
			<!-- 海关编码标题 -->
			<div class="hscode-header">
				<span class="hscode-number">{{ model.hscode || '8422303090' }}</span>
			</div>

			<!-- 基本信息区域 -->
			<div class="basic-info-section">
				<div class="info-row">
					<div class="info-item full-width">
						<span class="label">商品名称：</span>
						<span class="value">{{ model.gname || '其他包装机' }}</span>
					</div>
				</div>

				<div class="info-row">
					<div class="info-item full-width">
						<span class="label">商品英文名称：</span>
						<span class="value">{{ model.gnameEn || 'Other packaging machines' }}</span>
					</div>
				</div>

				<div class="info-row">
					<div class="info-item">
						<span class="label">计量单位：</span>
						<span class="value">{{ getUnitText(model.unit1) || '台/千克' }}</span>
					</div>
					<div class="info-item wide">
						<span class="label">海关监管条件：</span>
						<span class="value condition-value">
							<a-tag v-if="model.controLmark" color="blue" @click="showConditionDetail('A')">A</a-tag>
							<span v-else>无</span>
						</span>
					</div>
					<div class="info-item">
						<span class="label">检验检疫类别：</span>
						<span class="value condition-value">
							<a-tag v-if="model.inspMonitorCond" color="orange" @click="showInspectionDetail('R')">R</a-tag>
							<span v-else>无</span>
						</span>
					</div>
				</div>

				<div class="info-row">
					<div class="info-item full-width">
						<span class="label">申报要素：</span>
						<a-tooltip placement="top">
							<template slot="title">
								<div>申报要素详情</div>
							</template>
							<a-icon style="margin-right: 4px; color: #1890ff" type="question-circle" />
						</a-tooltip>
						<span class="value elements-value">
							{{
								model.sbys ||
									'*0:品牌类型；*1:出口享惠情况；*2:用途；*3:品牌（中文或外文名称）；*4:型号；5:GTIN；6:CAS；7:其他'
							}}
						</span>
					</div>
				</div>

				<!--				<div class="info-row">-->
				<!--					<div class="info-item full-width">-->
				<!--						<span class="label">申报要素范例：</span>-->
				<!--						<span class="value special-note">-->
				<!--              {{ model.declarationExample || '' }}-->
				<!--            </span>-->
				<!--					</div>-->
				<!--				</div>-->

				<div class="info-row">
					<div class="info-item">
						<span class="label">CIQ代码：</span>
						<span class="value link-value" @click="showCiqDetail">查看</span>
					</div>
					<div class="info-item">
						<span class="label">3C：</span>
						<span class="value">{{ model.ccc || '无' }}</span>
					</div>
					<div class="info-item">
						<span class="label">能效标识：</span>
						<span class="value link-value" @click="showEnergyDetail">查看</span>
					</div>
					<div class="info-item">
						<span class="label">水效标识：</span>
						<span class="value">{{ model.waterLabel || '无' }}</span>
					</div>
				</div>

				<div class="info-row">
					<div class="info-item full-width">
						<span class="label">特殊监管说明：</span>
						<span class="value">{{ model.specialSupervision || '无' }}</span>
					</div>
				</div>
			</div>

			<!-- 税费信息区域 -->
			<div class="tax-info-section">
				<!-- 进口税费 -->
				<div class="tax-section import-section">
					<div class="section-header">进口税费</div>
					<div class="tax-rows">
						<div class="tax-row">
							<span class="tax-label">暂定税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.mfnRate) || '8' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">最惠国税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.mfnRate) || '8' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">普通税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.generalRate) || '15' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">对美税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.usRate) || '25' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">对加拿大税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.canadaRate) || '无' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">消费税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.consumptionRate) || '3' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">增值税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.vatRate) || '13' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">信息技术产品最惠国税率(%)：</span>
							<span class="tax-value link-value" @click="showAgreementRate">查看</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">协定税率：</span>
							<span class="tax-value link-value" @click="showAgreementRate">查看</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">特惠税率：</span>
							<span class="tax-value link-value" @click="showSpecialRate">查看</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">从量、复合税率：</span>
							<span class="tax-value">{{ model.quantityRate || '无' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">RCEP：</span>
							<span class="tax-value link-value" @click="showRcepRate">查看</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">反倾销反补贴：</span>
							<span class="tax-value">{{ model.antiDumping || '无' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">备注：</span>
							<span class="tax-value">{{ model.importRemark || '' }}</span>
						</div>
					</div>
				</div>

				<!-- 出口税费 -->
				<div class="tax-section export-section">
					<div class="section-header">出口税费</div>
					<div class="tax-rows">
						<div class="tax-row">
							<span class="tax-label">出口退税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.exportRebateRate) || '13' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">出口关税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.exportTaxRate) || '0' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">出口暂定税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.exportTemporaryRate) || '无' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">出口特别关税率(%)：</span>
							<span class="tax-value">{{ formatTaxRate(model.exportSpecialRate) || '无' }}</span>
						</div>
						<div class="tax-row">
							<span class="tax-label">备注：</span>
							<span class="tax-value">{{ model.exportRemark || '' }}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 进口单证表格 -->
			<div class="import-docs-section">
				<div class="section-title import-docs-title">进口单证</div>

				<!-- 海关监管条件详情表格 -->
				<div v-if="supervisionTableData && supervisionTableData.length" class="supervision-subsection">
					<div class="subsection-title">海关监管条件</div>
					<a-table
						:columns="supervisionColumns"
						:dataSource="supervisionTableData"
						:pagination="false"
						bordered
						rowKey="code"
						size="small"
					>
						<template slot="action" slot-scope="text, record">
							<a @click="viewSupervisionDetail(record)">查看</a>
						</template>
					</a-table>
				</div>

				<!-- 检验检疫单证详情表格 -->
				<div v-if="inspectionTableData && inspectionTableData.length" class="inspection-subsection">
					<div class="subsection-title">检验检疫单证</div>
					<a-table
						:columns="inspectionColumns"
						:dataSource="inspectionTableData"
						:pagination="false"
						bordered
						rowKey="code"
						size="small"
					>
						<template slot="action" slot-scope="text, record">
							<a @click="viewInspectionDetail(record)">查看</a>
						</template>
					</a-table>
				</div>

				<!-- 无单证提示 -->
				<div
					v-if="
						(!supervisionTableData || !supervisionTableData.length) &&
							(!inspectionTableData || !inspectionTableData.length)
					"
					class="docs-content"
				>
					<div class="no-docs-message">无进口单证</div>
				</div>

				<!-- 备注区域 -->
				<div class="remarks-subsection">
					<div class="subsection-title">备注</div>
					<div class="remarks-content">
						{{ model.notes || '无备注信息' }}
					</div>
				</div>
			</div>

			<!-- 出口单证表格 -->
			<div class="export-docs-section">
				<div class="section-title export-docs-title">出口单证</div>

				<!-- 海关监管条件详情表格 -->
				<div
					v-if="exportSupervisionTableData && exportSupervisionTableData.length"
					class="export-supervision-subsection"
				>
					<div class="subsection-title">海关监管条件</div>
					<a-table
						:columns="supervisionColumns"
						:dataSource="exportSupervisionTableData"
						:pagination="false"
						bordered
						rowKey="code"
						size="small"
					>
						<template slot="action" slot-scope="text, record">
							<a @click="viewExportSupervisionDetail(record)">查看</a>
						</template>
					</a-table>
				</div>

				<!-- 无单证提示 -->
				<div v-if="!exportSupervisionTableData || !exportSupervisionTableData.length" class="docs-content">
					<div class="no-docs-message">无出口单证</div>
				</div>
			</div>
		</div>
	</j-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import { ajaxGetDictItems } from '@/api/api'

export default {
	name: 'ErpHscodesDetailModal',
	data() {
		return {
			visible: false,
			model: {},
			modalTitle: '海关商品编码详情',
			units: [], // 单位字典数据

			// 监管证件表格列
			supervisionColumns: [
				{
					title: '单证编号',
					dataIndex: 'code',
					width: 100,
					align: 'center'
				},
				{
					title: '单证名称',
					dataIndex: 'name',
					align: 'left'
				},
				{
					title: '办理流程',
					dataIndex: 'process',
					align: 'left',
					scopedSlots: { customRender: 'action' }
				}
			],

			// 检验检疫表格列
			inspectionColumns: [
				{
					title: '单证编号',
					dataIndex: 'code',
					width: 100,
					align: 'center'
				},
				{
					title: '单证名称',
					dataIndex: 'name',
					align: 'left'
				},
				{
					title: '办理流程',
					dataIndex: 'process',
					align: 'left',
					scopedSlots: { customRender: 'action' }
				}
			],

			// 数据
			supervisionData: [],
			inspectionData: [],
			exportSupervisionData: []
		}
	},

	computed: {
		// 获取监管条件表格数据
		supervisionTableData() {
			return this.supervisionData
		},

		// 获取检验检疫表格数据
		inspectionTableData() {
			return this.inspectionData
		},

		// 获取出口监管条件表格数据
		exportSupervisionTableData() {
			return this.exportSupervisionData
		}
	},

	created() {
		// 初始化加载单位字典
		this.initDictData()
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			const dictCode = 'erp_units,name,code'
			let dictOptions = JSON.parse(sessionStorage.getItem(dictCode))
			if (dictOptions != null && dictOptions.length > 0) {
				this.units = dictOptions
			} else {
				try {
					const res = await ajaxGetDictItems(dictCode, null)
					if (res.success) {
						sessionStorage.setItem(dictCode, JSON.stringify(res.result))
						this.units = res.result || []
					}
				} catch (error) {
					console.error('加载单位字典失败:', error)
				}
			}
		},

		// 显示详情
		async show(record) {
			this.visible = true
			this.model = { ...record }
			this.modalTitle = `海关商品编码详情 - ${record.hscode || '8422303090'}`

			// 加载监管证件和检验检疫信息
			await this.loadSupervisionData(record)
			await this.loadInspectionData(record)
			await this.loadExportSupervisionData(record)
		},

		// 关闭弹窗
		handleCancel() {
			this.visible = false
			this.model = {}
			this.supervisionData = []
			this.inspectionData = []
			this.exportSupervisionData = []
		},

		// 获取单位文本
		getUnitText(unitCode) {
			if (!unitCode) return '台/千克'

			// 从字典中查找
			const unit = this.units.find(u => u.value === unitCode || u.code === unitCode)
			if (unit) {
				return unit.text || unit.name || unitCode
			}

			return unitCode
		},

		// 格式化税率显示
		formatTaxRate(rate) {
			if (rate === null || rate === undefined || rate === '') return ''
			if (rate === '0' || rate === 0) return '0'
			return rate
		},

		// 加载监管证件信息
		async loadSupervisionData(record) {
			try {
				// 模拟数据
				this.supervisionData = [
					{
						code: 'A',
						name: '入境货物通关单',
						process: '查看'
					}
				]
			} catch (error) {
				console.error('加载监管证件信息失败:', error)
				this.supervisionData = []
			}
		},

		// 加载检验检疫信息
		async loadInspectionData(record) {
			try {
				// 模拟数据
				this.inspectionData = [
					{
						code: 'R-Q8',
						name:
							'《进出口电子产品质量口岸验证》，进口未列入《进出口电子产品检验检疫管制目录》的进出口电子产品，收用单位向目岸检验检疫机构提出其标的转移物用于现代的质量安全问题或担保的期间.',
						process: '查看'
					},
					{
						code: 'I-3-01-3',
						name: '《卫生证书》（包括《卫生证书》）',
						process: '查看'
					},
					{
						code: 'I-2-05',
						name:
							'《货运前检验证书》或《免<进出口电子产品货运前检验证书>进口货物情况声明》，海关总署授权检验机构签发的证明其检验证可作为法的流程，进口列入《进出口电子产品检验检疫管制目录》重点商品。',
						process: '查看'
					}
				]
			} catch (error) {
				console.error('加载检验检疫信息失败:', error)
				this.inspectionData = []
			}
		},

		// 显示监管条件详情
		showConditionDetail(condition) {
			this.$message.info(`查看监管条件 ${condition} 详情`)
		},

		// 显示检验检疫详情
		showInspectionDetail(condition) {
			this.$message.info(`查看检验检疫类别 ${condition} 详情`)
		},

		// 显示CIQ详情
		showCiqDetail() {
			this.$message.info('查看CIQ代码详情')
		},

		// 显示能效标识详情
		showEnergyDetail() {
			this.$message.info('查看能效标识详情')
		},

		// 显示协定税率
		showAgreementRate() {
			this.$message.info('查看协定税率详情')
		},

		// 显示特惠税率
		showSpecialRate() {
			this.$message.info('查看特惠税率详情')
		},

		// 显示RCEP税率
		showRcepRate() {
			this.$message.info('查看RCEP税率详情')
		},

		// 查看监管详情
		viewSupervisionDetail(record) {
			this.$message.info(`查看监管条件详情: ${record.name}`)
		},

		// 查看检验检疫详情
		viewInspectionDetail(record) {
			this.$message.info(`查看检验检疫详情: ${record.name}`)
		},

		// 加载出口监管证件信息
		async loadExportSupervisionData(record) {
			try {
				// 模拟数据
				this.exportSupervisionData = [
					{
						code: 'B',
						name: '出境货物通关单',
						process: '查看'
					}
				]
			} catch (error) {
				console.error('加载出口监管证件信息失败:', error)
				this.exportSupervisionData = []
			}
		},

		// 查看出口监管详情
		viewExportSupervisionDetail(record) {
			this.$message.info(`查看出口监管条件详情: ${record.name}`)
		}
	}
}
</script>

<style lang="less" scoped>
.hscode-detail-container {
	padding: 0;
	max-height: calc(100vh - 200px);
	overflow-y: auto;
	background: #f5f5f5;

	// 海关编码头部
	.hscode-header {
		background: #e6f7ff;
		color: #1890ff;
		padding: 8px 16px;
		display: flex;
		align-items: start;
		justify-content: start;

		.hscode-number {
			font-size: 16px;
			font-weight: 600;
		}
	}

	// 基本信息区域
	.basic-info-section {
		background: white;
		border: 1px solid #d9d9d9;

		.info-row {
			display: flex;
			border-bottom: 1px solid #d9d9d9;

			&:last-child {
				border-bottom: none;
			}

			.info-item {
				flex: 1;
				padding: 8px 12px;
				border-right: 1px solid #d9d9d9;
				display: flex;
				align-items: center;
				min-height: 36px;

				&:last-child {
					border-right: none;
				}

				&.full-width {
					flex: 2;
				}

				&.wide {
					flex: 1.5;
				}

				&.special-declaration {
					background: #fff7e6;
				}

				.label {
					color: #000;
					font-weight: 500;
					white-space: nowrap;
					margin-right: 8px;
				}

				.value {
					color: #333;
					flex: 1;

					&.condition-value {
						display: flex;
						align-items: center;
						flex-wrap: wrap;
					}

					&.elements-value {
						line-height: 1.5;
						color: #d4380d;
					}

					&.link-value {
						color: #1890ff;
						cursor: pointer;
						text-decoration: underline;

						&:hover {
							color: #40a9ff;
						}
					}

					.warning-text {
						color: #ff4d4f;
						font-size: 12px;
					}
				}
			}
		}
	}

	// 税费信息区域
	.tax-info-section {
		display: flex;
		margin-top: 16px;
		gap: 16px;

		.tax-section {
			flex: 1;
			background: white;
			border: 1px solid #d9d9d9;

			.section-header {
				background: #e6f7ff;
				padding: 8px 12px;
				font-weight: 600;
				color: #000;
				border-bottom: 1px solid #d9d9d9;
				text-align: center;
			}

			.tax-rows {
				padding: 0;

				.tax-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 6px 12px;
					border-bottom: 1px solid #f0f0f0;

					&:last-child {
						border-bottom: none;
					}

					.tax-label {
						color: #000;
						font-size: 13px;
						white-space: nowrap;
					}

					.tax-value {
						color: #333;
						font-weight: 500;
						text-align: right;

						&.link-value {
							color: #1890ff;
							cursor: pointer;
							text-decoration: underline;

							&:hover {
								color: #40a9ff;
							}
						}

						&.special-rate {
							display: flex;
							align-items: center;
							justify-content: flex-end;
						}
					}
				}
			}

			&.import-section .section-header {
				background: #f6ffed;
				color: #40a9ff;
			}

			&.export-section .section-header {
				background: #fff2e8;
				color: #40a9ff;
			}
		}
	}

	// 表格区域通用样式
	.import-docs-section,
	.export-docs-section {
		margin-top: 16px;
		background: white;
		border: 1px solid #d9d9d9;

		.section-title {
			background: #e6f7ff;
			padding: 8px 12px;
			font-weight: 600;
			color: #000;
			border-bottom: 1px solid #d9d9d9;

			&.import-docs-title {
				background: #e6f7ff;
				color: #000;
			}

			&.export-docs-title {
				background: #e6f7ff;
				color: #000;
			}
		}

		.docs-content {
			padding: 20px;
			text-align: center;

			.no-docs-message {
				color: #999;
				font-size: 14px;
			}
		}

		// 子区域样式
		.supervision-subsection,
		.inspection-subsection,
		.export-supervision-subsection {
			margin: 16px;
			border: 1px solid #e8e8e8;
			border-radius: 4px;

			.subsection-title {
				background: #f8f9fa;
				padding: 8px 12px;
				font-weight: 500;
				color: #333;
				border-bottom: 1px solid #e8e8e8;
				font-size: 14px;
			}
		}

		.supervision-subsection .subsection-title {
			background: #e6f7ff;
			color: #1890ff;
		}

		.inspection-subsection .subsection-title {
			background: #e6f7ff;
			color: #1890ff;
		}

		.export-supervision-subsection .subsection-title {
			background: #e6f7ff;
			color: #1890ff;
		}

		.remarks-subsection {
			.subsection-title {
				background: #f0f2f5;
				color: #333;
			}

			.remarks-content {
				padding: 12px;
				color: #333;
				line-height: 1.6;
				font-size: 14px;
			}
		}
	}

	// 表格样式
	/deep/ .ant-table {
		&.ant-table-small {
			> .ant-table-content {
				> .ant-table-body {
					margin: 0;

					> table {
						> .ant-table-thead > tr > th {
							background: #fafafa;
							color: rgba(0, 0, 0, 0.85);
							font-weight: 600;
							border-bottom: 1px solid #e8e8e8;
							padding: 8px;
						}

						> .ant-table-tbody > tr {
							&:hover {
								background: #f5f5f5;
							}

							> td {
								padding: 8px;
								border-bottom: 1px solid #f0f0f0;
							}
						}
					}
				}
			}
		}
	}

	// 滚动条美化
	&::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}

	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 3px;

		&:hover {
			background: #555;
		}
	}
}

// 响应式设计
@media (max-width: 1200px) {
	.hscode-detail-container {
		.tax-info-section {
			flex-direction: column;
		}
	}
}
</style>

