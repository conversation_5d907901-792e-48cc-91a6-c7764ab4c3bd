<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StorageRepairOrderMapper">

    <select id="listGoods" resultType="org.jeecg.modules.business.entity.StoreStocks">
        SELECT
            *
        FROM
            `store_stocks`
        WHERE
            CUSTOMER = #{storageRepairOrder.customer}
          AND STORE_CODE = #{storageRepairOrder.storeCode}
          AND DETAIL_TYPE = #{detailType}
          AND BEGIN_QTY > 0
        <if test="storageRepairOrder.copGno != null and storageRepairOrder.copGno != ''">
            AND COP_GNO LIKE CONCAT('%', #{storageRepairOrder.copGno}, '%')
        </if>
        <if test="storageRepairOrder.pn != null and storageRepairOrder.pn != ''">
            AND PN LIKE CONCAT('%', #{storageRepairOrder.pn}, '%')
        </if>
        <if test="storageRepairOrder.batchNo != null and storageRepairOrder.batchNo != ''">
            AND BATCH_NO LIKE CONCAT('%', #{storageRepairOrder.batchNo}, '%')
        </if>
        <if test="storageRepairOrder.areaCode != null and storageRepairOrder.areaCode != ''">
            AND AREA_NAME = #{storageRepairOrder.areaCode}
        </if>
    </select>
    <select id="listOldParts" resultType="org.jeecg.modules.business.entity.StoreStocks">
        SELECT
        *
        FROM
        `store_stocks`
        WHERE
        CUSTOMER = #{storageRepairOrder.customer}
        AND STORE_CODE = #{storageRepairOrder.storeCode}
        AND BEGIN_QTY > 0
        <if test="storageRepairOrder.copGno != null and storageRepairOrder.copGno != ''">
            AND COP_GNO LIKE CONCAT('%', #{storageRepairOrder.copGno}, '%')
        </if>
        <if test="storageRepairOrder.pn != null and storageRepairOrder.pn != ''">
            AND PN LIKE CONCAT('%', #{storageRepairOrder.pn}, '%')
        </if>
        <if test="storageRepairOrder.batchNo != null and storageRepairOrder.batchNo != ''">
            AND BATCH_NO LIKE CONCAT('%', #{storageRepairOrder.batchNo}, '%')
        </if>
        <if test="storageRepairOrder.areaCode != null and storageRepairOrder.areaCode != ''">
            AND AREA_NAME = #{storageRepairOrder.areaCode}
        </if>
    </select>
    <select id="getRepairByCond" resultType="org.jeecg.modules.business.entity.StorageRepairOrder">
        SELECT
            *
        FROM
            `storage_repair_order`
        WHERE
            CONCAT( COP_GNO, '|', '1' ) = #{searchCondition}
          AND `STATUS` = #{status}
          AND TENANT_ID = #{tenantId}
    </select>
    <select id="getInStorageCount" resultType="java.math.BigDecimal">
        SELECT
            SUM(IFNULL(STOCK_VAR, 0)) AS TOTAL_STOCK_VAR
        FROM
            `store_stocks_flow`
        WHERE
          IE_FLAG = 'I'
          AND (AREA_CODE LIKE '%待维修%' OR AREA_NAME LIKE '%待维修%')
          AND COP_GNO = #{storeStocks.copGno}
          AND BATCH_NO = #{storeStocks.batchNo}
    </select>
</mapper>
