<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.JgVFlySjZqHwsbCxdXxcxMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.JgVFlySjZqHwsbCxdXxcx">
        SELECT
            *
        FROM
            JG_V_Fly_SjZq_Hwsb_Cxd_Xxcx
        WHERE
            consigneeCname = #{customerName}
          AND updateTime BETWEEN #{starDate} AND #{lastDate}
        ORDER BY
            updateTime DESC
    </select>
</mapper>
