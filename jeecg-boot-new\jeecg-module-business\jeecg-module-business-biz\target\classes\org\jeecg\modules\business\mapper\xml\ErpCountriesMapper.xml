<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ErpCountriesMapper">
    <delete id="cleanData">

        DELETE
        FROM erp_countries

    </delete>
    <select id="selectData" resultType="java.lang.Integer">

        SELECT COUNT(*)
        FROM erp_countries

    </select>
    <select id="listCountriesByWarnLevel" resultType="java.lang.String">
        SELECT
            CODE
        FROM
            ERP_COUNTRIES
        WHERE
            ISENABLED = 1
          AND WARN_LEVEL = #{warnLevel}
    </select>
</mapper>