<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.RfqDetailMapper">

<!--    <select id="listMin" resultType="org.jeecg.modules.business.entity.RfqDetail">-->
    <select id="listMin" resultType="org.jeecg.modules.business.entity.RfqFreightRate">
        SELECT DISTINCT r.*
        FROM RfQ_DETAIL d LEFT JOIN RFQ_FREIGHT_RATE r ON r.ID = d.RFQ_ID
        <foreach collection="list" item="mp" separator="OR" open="WHERE">
         (r.START_PORT=#{mp.START_PORT}) AND (r.DEST_PORT=#{mp.DEST_PORT}) AND (r.TRANS_TYPE=#{mp.TRANS_TYPE}) AND (d.BOX_SIZE=#{mp.BOX_SIZE}) AND  (d.PRICE_BASE=#{mp.PRICE_BASE})
        </foreach>

        ORDER BY r.START_PORT,r.DEST_PORT,r.TRANS_TYPE,d.BOX_SIZE
    </select>

    <select id="listMinByRfqIds-1" resultType="org.jeecg.modules.business.entity.RfqDetail">
        SELECT d.*
        FROM RfQ_DETAIL d LEFT JOIN RFQ_FREIGHT_RATE r ON r.ID = d.RFQ_ID
        <foreach collection="list" item="rfqId" separator="," open="WHERE RFQ_ID IN(" close=")">#{rfqId}</foreach>
        GROUP BY r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE
    </select>

    <select id="listMinByRfqIds" resultType="org.jeecg.modules.business.entity.RfqFreightRate">
        SELECT LISTAGG(DISTINCT d.ID WITHIN GROUP(ORDER BY d.ID) IDS, MIN(d.PRICE_BASE)
        FROM RfQ_DETAIL d LEFT JOIN RFQ_FREIGHT_RATE r ON r.ID = d.RFQ_ID

        <foreach collection="list" item="rfqId" separator="," open="WHERE RFQ_ID IN(" close=")">#{rfqId}</foreach>
        GROUP BY r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE
    </select>

    <select id="listMinR" resultType="org.jeecg.modules.business.entity.RfqDetail" parameterType="Map">
        SELECT r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE, MIN(d.PRICE_BASE) AS PRICE_BASE
        FROM RfQ_DETAIL d LEFT JOIN RFQ_FREIGHT_RATE r ON r.ID = d.RFQ_ID

        GROUP BY r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE
    </select>
    <select id="listMinV" resultType="map" parameterType="Map">
        SELECT r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE, MIN(d.PRICE_BASE) AS PRICE_BASE
        FROM RfQ_DETAIL d LEFT JOIN RFQ_FREIGHT_RATE r ON r.ID = d.RFQ_ID

        GROUP BY r.START_PORT,r.DEST_PORT, r.TRANS_TYPE, d.BOX_SIZE
    </select>
</mapper>
