<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ErpCurrenciesMapper">
    <select id="getCurrenciesList" parameterType="org.jeecg.modules.business.entity.ErpCurrencies"
            resultType="org.jeecg.modules.business.entity.ErpCurrencies">

        SELECT
        ec.id,
        ec.code,
        ec.currency,
        ec.name,
        ec.enname,
        ec.rate,
        ec.currency_order
        FROM
        erp_currencies ec

<!--        <where>-->
<!--            ec.del_flag = 0-->
<!--            and ec.tenant_id  = #{tenantId,jdbcType=INTEGER}-->
<!--        </where>-->
        ORDER BY ec.currency_order desc
    </select>
    <delete id="cleanData">

        DELETE
        FROM erp_currencies

    </delete>
    <select id="selectData" resultType="java.lang.Integer">

        SELECT COUNT(*)
        FROM erp_currencies

    </select>
</mapper>