<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.OrderPaymentInfoMapper">
    <update id="updateByIdNew" parameterType="org.jeecg.modules.business.entity.OrderPaymentInfo">
        update order_payment_info set actual_payment_date = #{actualPaymentDate},
        payment_status = #{paymentStatus}
        where id = #{id}
    </update>
</mapper>