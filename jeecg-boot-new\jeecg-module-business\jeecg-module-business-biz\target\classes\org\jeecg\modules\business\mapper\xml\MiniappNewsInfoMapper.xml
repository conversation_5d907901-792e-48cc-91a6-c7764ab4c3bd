<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.MiniappNewsInfoMapper">
    <select id="getMiniAppNewsList" parameterType="org.jeecg.modules.business.entity.MiniappNewsInfo"
            resultType="org.jeecg.modules.business.entity.MiniappNewsInfo">

        SELECT
        id,
        news_picture,
        news_title,
        news_author,
        news_date


        FROM
        miniapp_news_info

        <where>
            del_flag = 0
        </where>

    </select>
    <select id="getMiniAppNewsContent" parameterType="org.jeecg.modules.business.entity.MiniappNewsInfo"
            resultType="org.jeecg.modules.business.entity.MiniappNewsInfo">

        SELECT
        news_title,
        news_content,
        news_author,
        news_date

        FROM
        miniapp_news_info

        <where>
            del_flag = 0
            and id = #{id,jdbcType=VARCHAR}
        </where>

    </select>
</mapper>