<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DomesticSuppliersInfoMapper">

    <select id="getSuppliersStatisticsVOForInside" parameterType="org.jeecg.modules.business.vo.SuppliersStatisticsVO"
            resultType="org.jeecg.modules.business.vo.SuppliersStatisticsVO">
        SELECT
        suppliersInfo.SUPPLIERS_FULL_NAME AS SELLER,
        COUNT( orderInfo.ID ) AS PRODUCT_SUM
        FROM
        DOMESTIC_SUPPLIERS_INFO suppliersInfo
        LEFT JOIN PURCHASE_ORDER_INFO orderInfo ON orderInfo.SELLER = suppliersInfo.ID
        WHERE
        orderInfo.STATUS = '3'

        <if test="suppliersStatisticsVO.tenantId != null">
            AND orderInfo.TENANT_ID = #{suppliersStatisticsVO.tenantId}
        </if>
        <if test="suppliersStatisticsVO.seller != null and suppliersStatisticsVO.seller !=''">
            AND orderInfo.SELLER LIKE #{suppliersStatisticsVO.seller}
        </if>

        GROUP BY suppliersInfo.SUPPLIERS_FULL_NAME
        ORDER BY PRODUCT_SUM DESC

    </select>

    <select id="getSuppliersStatisticsVOForOutside" parameterType="org.jeecg.modules.business.vo.SuppliersStatisticsVO"
            resultType="org.jeecg.modules.business.vo.SuppliersStatisticsVO">

        SELECT
        suppliersInfo.SUPPLIERS_FULL_NAME AS SELLER,
        COUNT(orderInfo.ID) AS PRODUCT_SUM
        FROM
        DOMESTIC_SUPPLIERS_INFO suppliersInfo
        LEFT JOIN ORDER_INFO orderInfo ON orderInfo.OVERSEAS_PAYER_INFO_ID = suppliersInfo.ID
        <where>
            suppliersInfo.DEL_FLAG = '0'
            AND orderInfo.DEL_FLAG = '0'
            AND orderInfo.order_status in (3,9)
            AND orderInfo.pay_money_flag = 1
            <if test="suppliersStatisticsVO.insideOrOutside != null and suppliersStatisticsVO.insideOrOutside != ''">
                AND suppliersInfo.INSIDE_OR_OUTSIDE = #{suppliersStatisticsVO.insideOrOutside}
            </if>
            <if test="suppliersStatisticsVO.tenantId != null">
                AND suppliersInfo.TENANT_ID = #{suppliersStatisticsVO.tenantId}
            </if>
            <if test="suppliersStatisticsVO.seller != null and suppliersStatisticsVO.seller !=''">
                AND suppliersInfo.SUPPLIERS_FULL_NAME LIKE #{suppliersStatisticsVO.seller}
            </if>
        </where>
        GROUP BY suppliersInfo.SUPPLIERS_FULL_NAME
        ORDER BY PRODUCT_SUM DESC;
    </select>
    <select id="getDomesticSuppliersInfoByCond"
            resultType="org.jeecg.modules.business.entity.DomesticSuppliersInfo">
        SELECT
            *
        FROM
            `domestic_suppliers_info`
        WHERE
            ID = #{searchText}
           OR SUPPLIERS_FULL_NAME = #{searchText}
            LIMIT 1
    </select>

</mapper>