<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ForeignExchangeCollectionInfoMapper">
    <!-- 一览数据取得 -->
    <select id="queryPageCollectionDetailsList"
            resultType="org.jeecg.modules.business.entity.ForeignExchangeCollectionInfo">
        SELECT DISTINCT
        allFec.id,
        allFec.create_by,
        allFec.create_time,
        allFec.tenant_id,
        allFec.registration_no,
        allFec.sys_user_id,
        allFec.collection_date,
        allFec.customer_name,
        allFec.collection_type,
        allFec.collection_account,
        allFec.import_currency_type,
        allFec.entry_currency_type,
        allFec.rate,
        allFec.entry_amount,
        allFec.import_amount,
        allFec.balance,
        allFec.del_flag,
        allFec.update_time
        FROM
        (
        SELECT
        DISTINCT (fec1.id) AS id,
        fec1.create_by,
        fec1.create_time,
        fec1.tenant_id,
        fec1.registration_no,
        fec1.sys_user_id,
        fec1.collection_date,
        fec1.customer_name,
        fec1.collection_type,
        fec1.collection_account,
        fec1.import_currency_type,
        fec1.entry_currency_type,
        fec1.rate,
        fec1.entry_amount,
        fec1.import_amount,
        fec1.balance,
        fec1.del_flag,
        fec1.update_time
        FROM
        matching_history_info his
        INNER JOIN order_info info ON his.order_info_id = info.id
        AND info.order_status != '9'
        AND info.del_flag = 0
        AND ( info.receive_money_flag = 0 OR info.has_pay_money_flag = 0 )
        INNER JOIN foreign_exchange_collection_info fec1 ON fec1.id = his.foreign_exchange_collection_id
        AND fec1.balance = 0 UNION ALL
        SELECT
        fec2.id,
        fec2.create_by,
        fec2.create_time,
        fec2.tenant_id,
        fec2.registration_no,
        fec2.sys_user_id,
        fec2.collection_date,
        fec2.customer_name,
        fec2.collection_type,
        fec2.collection_account,
        fec2.import_currency_type,
        fec2.entry_currency_type,
        fec2.rate,
        fec2.entry_amount,
        fec2.import_amount,
        fec2.balance,
        fec2.del_flag,
        fec2.update_time
        FROM
        foreign_exchange_collection_info fec2
        WHERE
        fec2.del_flag = 0
        AND fec2.balance > 0
        ) allFec
        ${ew.customSqlSegment}
<!--        <where>-->
<!--            <if test="queryWrapper.tenantId != null">-->
<!--                and allFec.tenant_id = #{queryWrapper.tenantId,jdbcType=INTEGER}-->
<!--            </if>-->
<!--            <if test="queryWrapper.customerName != null">-->
<!--                and allFec.customer_name = #{queryWrapper.customerName,jdbcType=VARCHAR}-->
<!--            </if>-->
<!--            <if test="queryWrapper.importCurrencyType != null">-->
<!--                and allFec.import_currency_type = #{queryWrapper.importCurrencyType,jdbcType=INTEGER}-->
<!--            </if>-->
<!--            <if test="queryWrapper.registrationNo != null">-->
<!--                and allFec.registration_no like concat("%",#{queryWrapper.registrationNo,jdbcType=VARCHAR},"%")-->
<!--            </if>-->
<!--            <if test="queryWrapper.collectionDate != null">-->
<!--                and allFec.collection_date = #{queryWrapper.collectionDate,jdbcType=DATE}-->
<!--            </if>-->
<!--        </where>-->
    </select>
</mapper>