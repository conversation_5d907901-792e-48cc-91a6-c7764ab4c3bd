<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PassPortHeadMapper">
    <!--根据核放单统一编号修改账册的推送标识-->
    <update id="setEmsPushStatusBySeqNo">
        UPDATE PTS_EMS_AIMG a
            JOIN STOCK_GOODS_TYPE c ON a.G_NO = c.ORIACT_GDS_SEQNO
            JOIN STOCK_HEAD_TYPE b ON c.STOCK_ID = b.ID
            JOIN PASS_PORT_HEAD e ON b.ID = e.RELATION_ID
            SET
                a.PUSH_STATUS = '0'
        WHERE
            b.CREATE_PASS_PORT = '1'
          AND a.EMS_NO = 'L4301D18A001'
          AND e.SEQ_NO = #{seqNo}
    </update>

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.PassPortHead">
        SELECT
            a.*
        FROM
            `pass_port_head` a
        <where>
            a.TENANT_ID = #{passPortVO.tenantId}
            <if test="passPortVO.audited != null and passPortVO.audited !=''">
                AND a.AUDITED = #{passPortVO.audited}
            </if>
            <if test="passPortVO.send != null and passPortVO.send !=''">
                AND a.SEND = #{passPortVO.send}
            </if>
            <if test="passPortVO.status != null and passPortVO.status !=''">
                AND a.STATUS = #{passPortVO.status}
            </if>
            <if test="passPortVO.bindTypecd != null and passPortVO.bindTypecd !=''">
                AND a.BIND_TYPECD = #{passPortVO.bindTypecd}
            </if>
            <if test="passPortVO.passportTypecd != null and passPortVO.passportTypecd !=''">
                AND a.PASSPORT_TYPECD = #{passPortVO.passportTypecd}
            </if>
            <if test="passPortVO.rltTbTypecd != null and passPortVO.rltTbTypecd !=''">
                AND a.RLT_TB_TYPECD = #{passPortVO.rltTbTypecd}
            </if>
            <if test="passPortVO.rltNo != null and passPortVO.rltNo !=''">
                AND a.RLT_NO LIKE CONCAT('%', #{passPortVO.rltNo}, '%')
            </if>
            <if test="passPortVO.passportNo != null and passPortVO.passportNo !=''">
                AND a.PASSPORT_NO LIKE CONCAT('%', #{passPortVO.passportNo}, '%')
            </if>
            <if test="passPortVO.vehicleNo != null and passPortVO.vehicleNo !=''">
                AND a.VEHICLE_NO LIKE CONCAT('%', #{passPortVO.vehicleNo}, '%')
            </if>
            <if test="passPortVO.ioTypecd != null and passPortVO.ioTypecd !=''">
                AND a.IO_TYPECD = #{passPortVO.ioTypecd}
            </if>
            <if test="passPortVO.passStatus != null and passPortVO.passStatus !=''">
                AND a.PASS_STATUS = #{passPortVO.passStatus}
            </if>
            <if test="passPortVO.seqNo != null and passPortVO.seqNo !=''">
                AND a.SEQ_NO LIKE CONCAT('%', #{passPortVO.seqNo}, '%')
            </if>
            <if test="passPortVO.areainEtpsNo != null and passPortVO.areainEtpsNo !=''">
                AND a.AREAIN_ETPS_NO LIKE CONCAT('%', #{passPortVO.areainEtpsNo}, '%')
            </if>
            <if test="passPortVO.etpsPreentNo != null and passPortVO.etpsPreentNo !=''">
                AND a.ETPS_PREENT_NO LIKE CONCAT('%', #{passPortVO.etpsPreentNo}, '%')
            </if>
            <if test="passPortVO.areainEtpsNm != null and passPortVO.areainEtpsNm !=''">
                AND a.AREAIN_ETPS_NM LIKE CONCAT('%', #{passPortVO.areainEtpsNm}, '%')
            </if>
            <if test="passPortVO.dclTimeStart != null and passPortVO.dclTimeStart != ''">
                AND DATE_FORMAT(a.DCL_TIME, '%Y-%m-%d') &gt;= #{passPortVO.dclTimeStart}
            </if>
            <if test="passPortVO.dclTimeEnd != null and passPortVO.dclTimeEnd != ''">
                AND DATE_FORMAT(a.DCL_TIME, '%Y-%m-%d') &lt;= #{passPortVO.dclTimeEnd}
            </if>
            <if test="passPortVO.starCreateDate != null and passPortVO.starCreateDate != ''">
                AND DATE_FORMAT(a.CREATE_DATE, '%Y-%m-%d') &gt;= #{passPortVO.starCreateDate}
            </if>
            <if test="passPortVO.lastCreateDate != null and passPortVO.lastCreateDate != ''">
                AND DATE_FORMAT(a.CREATE_DATE, '%Y-%m-%d') &lt;= #{passPortVO.lastCreateDate}
            </if>
        </where>
        ORDER BY
        a.CREATE_DATE DESC
    </select>
    <select id="listPasPortHeadAndNemsInvt" resultType="org.jeecg.modules.business.entity.excel.PassPortByI">
        SELECT
            PASS_PORT_HEAD.ID,
            PASS_PORT_HEAD.PASSPORT_NO,
            PASS_PORT_HEAD.DCL_TIME,
            PASS_PORT_HEAD.ETPS_PREENT_NO,
            PASS_PORT_HEAD.RLT_NO,
            PASS_PORT_HEAD.RECEIPT_DATE,
            NEMS_INVT_HEAD.BOND_INVT_NO,
            NEMS_INVT_LIST.DCL_QTY,
            NEMS_INVT_LIST.DCL_UNITCD,
            NEMS_INVT_LIST.GDS_MTNO,
            NEMS_INVT_LIST.HSNAME
        FROM PASS_PORT_HEAD
        LEFT JOIN NEMS_INVT_HEAD ON NEMS_INVT_HEAD.ID = PASS_PORT_HEAD.RELATION_ID
        LEFT JOIN NEMS_INVT_LIST ON NEMS_INVT_HEAD.ID = NEMS_INVT_LIST.INV_ID
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ORDER BY PASS_PORT_HEAD.CREATE_DATE
    </select>
</mapper>
