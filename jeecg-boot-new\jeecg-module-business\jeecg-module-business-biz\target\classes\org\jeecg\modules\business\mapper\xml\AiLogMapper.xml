<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.AiLogMapper">

    <insert id="saveLog">
        insert into ai_log (id, log_type, log_content, method, operate_type, REQUEST_URL, request_type,
                             request_param, ip, userid, username, cost_time, create_time,
                             tenant_id, source_id, remark, is_success)
        values(
                  #{dto.id,jdbcType=VARCHAR},
                  #{dto.logType,jdbcType=INTEGER},
                  #{dto.logContent,jdbcType=VARCHAR},
                  #{dto.method,jdbcType=VARCHAR},
                  #{dto.operateType,jdbcType=INTEGER},
                  #{dto.requestUrl,jdbcType=VARCHAR},
                  #{dto.requestType,jdbcType=VARCHAR},
                  #{dto.requestParam,jdbcType=VARCHAR},
                  #{dto.ip,jdbcType=VARCHAR},
                  #{dto.userid,jdbcType=VARCHAR},
                  #{dto.username,jdbcType=VARCHAR},
                  #{dto.costTime,jdbcType=BIGINT},
                  #{dto.createTime,jdbcType=TIMESTAMP},
                  #{dto.tenantId,jdbcType=VARCHAR},
                  #{dto.sourceId,jdbcType=VARCHAR},
                  #{dto.remark,jdbcType=VARCHAR},
                  #{dto.isSuccess,jdbcType=CHAR}
              )
    </insert>
</mapper>
