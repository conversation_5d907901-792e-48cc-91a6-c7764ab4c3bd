<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ProductCustomizeItemCategoryInfoMapper">

    <select id="queryList" parameterType="org.jeecg.modules.business.entity.ProductCustomizeItemCategoryInfo" resultType="org.jeecg.modules.business.entity.ProductCustomizeItemCategoryInfo">

        select c.id,c.item_id
        from product_customize_item_category_info c
        <where>
            c.del_flag = '0'
            and c.product_category_info_id = #{productCategoryInfoId,jdbcType=VARCHAR}
            <if test="tenantId != null">
                and c.tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
        </where>

    </select>

    <delete id="deleteCategoryId" parameterType="java.lang.String">
        delete from
        product_customize_item_category_info
        <where>
            product_category_info_id = #{productCategoryInfoId,jdbcType=VARCHAR}
        </where>

    </delete>

</mapper>