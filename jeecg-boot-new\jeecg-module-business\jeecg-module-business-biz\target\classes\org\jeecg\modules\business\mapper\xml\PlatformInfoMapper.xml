<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.PlatformInfoMapper">

    <select id="getProductSalesStatistics" parameterType="org.jeecg.modules.business.vo.ProductSalesStatisticsVO" resultType="org.jeecg.modules.business.vo.ProductSalesStatisticsVO">
        SELECT
            product.CHINESE_NAME,
            SUM( product.SHIPMENT_QUANTITY ) AS PRODUCT_SUM
        FROM
            ORDER_INFO orderInfo,
            ORDER_PRODUCT_INFO product,
            PRODUCT_INFO info
        WHERE
            orderInfo.ID = product.ORDER_INFO_ID
            AND product.PRODUCT_ID = info.ID
            AND orderInfo.DEL_FLAG = '0'
            AND product.DEL_FLAG = '0'
            AND orderInfo.order_status in (3,9)
            AND orderInfo.pay_money_flag = 1
            AND orderInfo.receive_money_flag = 1
            AND info.DEL_FLAG ='0'
        <if test="productSalesStatisticsVO.ieFlag != null">
            AND orderInfo.IE_FLAG = #{productSalesStatisticsVO.ieFlag}
        </if>
        <if test="productSalesStatisticsVO.tenantId != null">
            AND orderInfo.tenant_id = #{productSalesStatisticsVO.tenantId,jdbcType=INTEGER}
        </if>
        <if test="productSalesStatisticsVO.chineseName != null and productSalesStatisticsVO.chineseName != ''">
            AND product.CHINESE_NAME LIKE #{productSalesStatisticsVO.chineseName}
        </if>
        <if test="productSalesStatisticsVO.queryTimeStart != null and productSalesStatisticsVO.queryTimeStart != ''
            and productSalesStatisticsVO.queryTimeEnd != null and productSalesStatisticsVO.queryTimeEnd != ''">
            AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &gt;= #{productSalesStatisticsVO.queryTimeStart}
            AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &lt;= #{productSalesStatisticsVO.queryTimeEnd}
        </if>
        GROUP BY product.CHINESE_NAME ORDER BY  PRODUCT_SUM DESC;
    </select>

    <select id="getProductSalesStatisticsTop" parameterType="org.jeecg.modules.business.vo.ProductSalesStatisticsVO" resultType="org.jeecg.modules.business.vo.ProductSalesStatisticsVO">
        SELECT
            product.CHINESE_NAME,
            SUM( product.SHIPMENT_GOODS_VALUE ) AS PRODUCT_VALUE_SUM
        FROM
            ORDER_INFO orderInfo,
            ORDER_PRODUCT_INFO product
        WHERE
            orderInfo.ID = product.ORDER_INFO_ID
            AND orderInfo.DEL_FLAG = '0'
            AND product.DEL_FLAG = '0'
        <if test="productSalesStatisticsVO.ieFlag != null">
            AND orderInfo.IE_FLAG = #{productSalesStatisticsVO.ieFlag}
        </if>
        <if test="productSalesStatisticsVO.tenantId != null">
            AND orderInfo.tenant_id = #{productSalesStatisticsVO.tenantId,jdbcType=INTEGER}
        </if>
        <if test="productSalesStatisticsVO.chineseName != null and productSalesStatisticsVO.chineseName != ''">
            AND product.CHINESE_NAME LIKE #{productSalesStatisticsVO.chineseName}
        </if>
        <if test="productSalesStatisticsVO.queryTimeStart != null and productSalesStatisticsVO.queryTimeStart != ''
            and productSalesStatisticsVO.queryTimeEnd != null and productSalesStatisticsVO.queryTimeEnd != ''">
            AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &gt;= #{productSalesStatisticsVO.queryTimeStart}
            AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &lt;= #{productSalesStatisticsVO.queryTimeEnd}
        </if>
        GROUP BY product.CHINESE_NAME ORDER BY  PRODUCT_VALUE_SUM DESC;
    </select>

</mapper>