<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.MiniappMarketingInfoMapper">
    <select id="getMiniAppMarketingList" parameterType="org.jeecg.modules.business.entity.MiniappMarketingInfo"
            resultType="org.jeecg.modules.business.entity.MiniappMarketingInfo">

        SELECT
        id,
        marketing_picture,
        marketing_title

        FROM
        miniapp_marketing_info

        <where>
            show_flag = 1
            and del_flag = 0
        </where>
    </select>
    <select id="getMiniAppMarketingContent" parameterType="org.jeecg.modules.business.entity.MiniappMarketingInfo"
            resultType="org.jeecg.modules.business.entity.MiniappMarketingInfo">

        SELECT
        marketing_title,
        marketing_content,
        marketing_organizer,
        marketing_date

        FROM
        miniapp_marketing_info

        <where>
            show_flag = 1
            and del_flag = 0
            and id = #{id,jdbcType=VARCHAR}
        </where>
    </select>
</mapper>