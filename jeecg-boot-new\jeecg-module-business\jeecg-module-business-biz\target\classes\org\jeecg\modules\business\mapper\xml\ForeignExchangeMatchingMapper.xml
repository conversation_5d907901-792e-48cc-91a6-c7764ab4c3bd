<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ForeignExchangeMatchingMapper">
    <!-- 一览数据取得 -->
    <select id="queryForeignExchangeMatching" parameterType="org.jeecg.modules.business.entity.ForeignExchangeMatching"
            resultType="org.jeecg.modules.business.entity.ForeignExchangeMatching">
        SELECT
        info.id,
        info.tenant_id,
        info.create_by,
        info.create_time,
        info.update_time,
        info.order_protocol_no,
        info.order_no_nine_seven,
        info.order_stock_no,
        info.supervision_mode,
        info.trading_country,
        info.overseas_payer_info_id,
        info.customs_declaration_currency,
        info.exchange_collection_type,
        info.total_contract_amount,
        info.invoice_no,
        info.export_contract_no,
        info.customer_service_manager,
        info.sales_manager,
        info.customs_number,
        info.delivery_numbers,
        info.estimated_shipment_date,
        info.receiver,
        info.exit_clearance,
        info.freight_amount,
        info.premium_amount,
        info.miscellaneous_amount,
        info.domestic_source_of_goods,
        info.final_contry,
        info.trading_type,
        info.customs_broker_id,
        info.close_date,
        info.overseas_warehouse_name,
        info.overseas_warehouse_address,
        info.overseas_warehouse_country,
        info.overseas_warehouse_platform,
        info.foreign_exchange_amount,
        info.order_status,
        info.receive_money_flag,
        info.pay_money_flag,
        info.has_pay_money_flag,
        info.logistics_service_mode,
        info.del_flag,
        info.remarks,
        info.remind_status
        FROM
        order_info info
        <where>
            info.order_status != '9'
            AND info.del_flag = 0
            AND (info.receive_money_flag = 0
            OR info.has_pay_money_flag = 0 )
            <if test="foreignExchangeMatching.tenantId != null">
                and info.tenant_id = #{foreignExchangeMatching.tenantId,jdbcType=INTEGER}
            </if>
            <if test="foreignExchangeMatching.overseasPayerInfoId != null">
                and info.overseas_payer_info_id = #{foreignExchangeMatching.overseasPayerInfoId,jdbcType=VARCHAR}
            </if>
            <if test="foreignExchangeMatching.customsDeclarationCurrency != null">
                and info.customs_declaration_currency = #{foreignExchangeMatching.customsDeclarationCurrency,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY
        info.receive_money_flag ASC,
        info.create_time DESC
    </select>
    <select id="getMatchingAmount" parameterType="org.jeecg.modules.business.entity.MatchingHistoryInfo"
            resultType="org.jeecg.modules.business.entity.MatchingHistoryInfo">
        SELECT
        *
        FROM
        (
        SELECT
        h.id,
        h.foreign_exchange_collection_id,
        h.order_info_id,
        h.create_by,
        h.create_time,
        h.matching_type,
        h.matching_amount
        FROM
        ( SELECT max( create_time ) AS create_time FROM matching_history_info GROUP BY foreign_exchange_collection_id, order_info_id ) a
        LEFT JOIN matching_history_info h ON h.create_time = a.create_time
        WHERE
        h.del_flag = 0
        AND h.foreign_exchange_collection_id = #{foreignExchangeCollectionId}
        AND h.order_info_id = #{orderInfoId}
        ) his
    </select>
    <!-- 一览数据取得 -->
    <select id="pageListOrderPayReview"
            resultType="org.jeecg.modules.business.entity.ForeignExchangeMatching">
        SELECT DISTINCT
        ( a.id ),
        a.tenant_id,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.order_protocol_no,
        a.order_no_nine_seven,
        a.order_stock_no,
        a.supervision_mode,
        a.trading_country,
        a.overseas_payer_info_id,
        a.customs_declaration_currency,
        a.exchange_collection_type,
        a.total_contract_amount,
        a.invoice_no,
        a.export_contract_no,
        a.customer_service_manager,
        a.sales_manager,
        a.customs_number,
        a.delivery_numbers,
        a.estimated_shipment_date,
        a.receiver,
        a.exit_clearance,
        a.freight_amount,
        a.freight_amount_type,
        a.unit_type_freight,
        a.premium_amount,
        a.premium_amount_type,
        a.unit_type_premium,
        a.miscellaneous_amount,
        a.domestic_source_of_goods,
        a.final_contry,
        a.trading_type,
        a.customs_broker_id,
        a.close_date,
        a.overseas_warehouse_name,
        a.overseas_warehouse_address,
        a.overseas_warehouse_country,
        a.overseas_warehouse_platform,
        a.foreign_exchange_amount,
        a.order_status,
        a.receive_money_flag,
        a.pay_money_flag,
        a.has_pay_money_flag,
        a.logistics_service_mode,
        a.push_status,
        a.del_flag,
        a.remarks,
        a.remind_status,
        a.incoming_notice_flag,
        a.customs_area_info_id
        FROM
        (
        SELECT
        info.*
        FROM
        order_info info
        INNER JOIN order_payment_info pay ON pay.order_info_id = info.id
        AND pay.del_flag = 0
        ORDER BY
        pay.update_time DESC
        ) a
        ${ew.customSqlSegment}
    </select>
</mapper>