<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.OrderInfoMapper">
    <!--    查询订单信息-->
    <select id="getOrderById" parameterType="org.jeecg.modules.business.entity.OrderInfoBiz"
            resultType="org.jeecg.modules.business.entity.OrderInfoBiz">
        SELECT
        o.*,
        t.update_time AS t_update_time,
        r.update_time AS r_update_time,
        s.update_time AS s_update_time,
        c.update_time AS c_update_time,
        t.id AS tid,
        t.shipping_type,
        t.country_arrival,
        t.departure_port,
        t.port_destination_name,
        t.port_destination,
        t.price_protocol,
        t.overflow_rate,
        t.shipping_mark,
        t.etd_date,
        t.transshipment_port_name,
        t.transshipment_port,
        t.insurance_coefficient,
        t.insurance_rate,
        t.total_name_goods,
        t.logistics_service_mode,
        t.transport_name,
        t.voy,
        t.destination,
        t.destinationid,
        t.portdischarge,
        t.portdischargeid,
        t.packs,
        t.weight,
        t.cbm,
        t.kindpkgs,
        t.CONTAINER_QUANTITY,
        s.id AS sid,
        s.consignment_note_date,
        s.term_validity_date,
        s.dr_no,
        s.consignment_type,
        s.consignment_numbers,
        s.consignment_requirement,
        s.pre_carriage_by,
        s.freight_payment_type,
        s.customs_declaration_number,
        s.export_agent,
        s.settlement_exchange_type,
        s.ocean_freight_amount,
        s.is_exchange_ship,
        s.is_in_batches,
        s.trailer_mode,
        s.box_type_and_volume,
        s.realname,
        s.phone,
        s.notifier,
        s.notifier_is_receiver_flag,
        s.delivery_place,
        r.id AS rid,
        r.logistics_id,
        r.enterprise_info_id,
        r.delivery_date,
        r.cut_order_date,
        r.cut_port_date,
        r.container,
        r.goods_description,
        r.receiving_location,
        r.receiving_cotact_name,
        r.receiving_cotact_phone,
        r.correspondence_content,
        r.total_rough_weight,
        r.total_specifications,
        c.id AS cid,
        c.pre_entry_no,
        c.domestic_suppliers_info_id,
        c.nature_levy,
        c.license_key,
        c.attached_doc_no,
        c.is_special_relationship,
        c.is_price_impact_confirmation,
        c.is_payment_of_royalties,
        c.is_self_reporting_payment,
        c.declaration_date,
        c.record_number,
        c.exemption,
        c.total_gross_weight,
        c.total_net_weight
        FROM
        order_info o
        LEFT JOIN order_transportation_info t ON o.id = t.order_info_id
        AND o.tenant_id = t.tenant_id
        AND t.del_flag = 0
        LEFT JOIN order_shipping_booking_info s ON o.id = s.order_info_id
        AND o.tenant_id = s.tenant_id
        AND s.del_flag = 0
        LEFT JOIN order_receipt_notice_info r ON o.id = r.order_info_id
        AND o.tenant_id = r.tenant_id
        AND r.del_flag = 0
        LEFT JOIN order_customs_dec_export_info c ON o.id = c.order_info_id
        AND o.tenant_id = c.tenant_id
        AND c.del_flag = 0
        <where>
            o.id = #{id,jdbcType=VARCHAR}
            AND o.tenant_id = #{tenantId,jdbcType=VARCHAR}
            AND o.del_flag = 0
        </where>
    </select>

<!--    查询订单概览信息-->
    <select id="getOverviewInfoByOrderInfoId" parameterType="org.jeecg.modules.business.entity.OrderOverviewInfoBiz"
            resultType="org.jeecg.modules.business.entity.OrderOverviewInfoBiz">
        SELECT
        o.*,
        c.declaration_date,
        t.shipping_type,
        t.transport_name,
        t.voy,
        c.domestic_suppliers_info_id,
        c.nature_levy,
        t.country_arrival,
        t.port_destination,
        t.port_destination_name,
        t.departure_port,
        c.total_gross_weight,
        c.total_net_weight
        FROM
        order_info o
        LEFT JOIN order_customs_dec_export_info c ON o.id = c.order_info_id
        AND o.tenant_id = c.tenant_id
        AND c.del_flag = 0
        LEFT JOIN order_transportation_info t ON o.id = t.order_info_id
        AND o.tenant_id = t.tenant_id
        AND t.del_flag = 0
        <where>
            o.id = #{id,jdbcType=VARCHAR}
            AND o.tenant_id = #{tenantId,jdbcType=VARCHAR}
            AND o.del_flag = 0
        </where>
    </select>

    <!--    查询订单概览信息-->
    <select id="getEnterpriseInfoInfoId" parameterType="org.jeecg.modules.business.entity.EnterpriseInfo"
            resultType="org.jeecg.modules.business.entity.EnterpriseInfo">
        select
        e.sditds_userloginname,
        e.sditds_userpassowrd,
        e.private_key_certificate,
        e.message_transmission_number
        from enterprise_info e
        <where>
             e.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.OrderInfo">
        SELECT
        ORDER_INFO.*
        FROM
        ORDER_INFO left join
        ORDER_PRODUCT_INFO on ORDER_INFO.ID=ORDER_PRODUCT_INFO.ORDER_INFO_ID
        <if test="hasRelDecNos != null and hasRelDecNos != ''">
            LEFT JOIN dec_head ON ORDER_INFO.ID = dec_head.APPLY_NUMBER
        </if>
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        GROUP BY ORDER_INFO.ID
        ORDER BY ORDER_INFO.CREATE_TIME DESC
    </select>
    <select id="getUserIdByUserName" resultType="java.lang.String">
        select id from SYS_USER
        WHERE USERNAME=#{userName}

    </select>
    <select id="isexistInvtNo" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            *
        FROM
            `nems_invt_head`
        WHERE
            ETPS_INNER_INVT_NO = #{invtNo}
    </select>
</mapper>
