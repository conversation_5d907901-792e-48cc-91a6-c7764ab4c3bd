<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ContractMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.Contract">
        SELECT
            *
        FROM
            CONTRACT
        <where>
            TENANT_ID = #{contract.tenantId}
            <if test="contract.contractNo != null and contract.contractNo != ''">
                AND CONTRACT_NO LIKE CONCAT('%', #{contract.contractNo}, '%')
            </if>
            <if test="contract.jiaCustomer != null and contract.jiaCustomer != ''">
                AND JIA_CUSTOMER LIKE CONCAT('%', #{contract.jiaCustomer}, '%')
            </if>
            <if test="contract.yiCustomer != null and contract.yiCustomer != ''">
                AND YI_CUSTOMER LIKE CONCAT('%', #{contract.yiCustomer}, '%')
            </if>
            <if test="contract.contractFlag != null and contract.contractFlag != ''">
                AND CONTRACT_FLAG = #{contract.contractFlag}
            </if>
            <if test="contract.settlementType != null and contract.settlementType != ''">
                AND SETTLEMENT_TYPE = #{contract.settlementType}
            </if>
            <if test="contract.settlementMode != null and contract.settlementMode != ''">
                AND SETTLEMENT_MODE = #{contract.settlementMode}
            </if>
            <if test="contract.contractType != null and contract.contractType != ''">
                AND CONTRACT_TYPE = #{contract.contractType}
            </if>
        </where>
        ORDER BY CREATE_DATE DESC
    </select>
</mapper>
