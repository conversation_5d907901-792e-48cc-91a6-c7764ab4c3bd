<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ErpCiqMapper">
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CODE_TS, CIQ_CODE, CIQ_NAME, FLAG, GOODS_ATTR, LASTUPDATE, UPDATE_INFO
    </sql>
    <select id="listCiqByCodeTsAndCiqCode" resultType="org.jeecg.modules.business.entity.ErpCiq">
        SELECT
        <include refid="Base_Column_List"/>
        FROM erp_ciq WHERE CONCAT(CODE_TS,'|',CIQ_CODE) IN
        <foreach item="sqlCode" collection="codeTsAndCiqCode" open="(" close=")" separator=",">
            #{sqlCode}
        </foreach>
    </select>
</mapper>
