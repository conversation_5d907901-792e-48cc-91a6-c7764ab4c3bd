<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ProductCustomizeItemInfoMapper">

    <select id="queryName" parameterType="org.jeecg.modules.business.entity.ProductCustomizeItemInfo" resultType="org.jeecg.modules.business.entity.ProductCustomizeItemInfo">

        select
        p.id,
        p.customize_item_name
        from
        product_customize_item_info p
        <where>
            p.del_flag = 0
            <if test="tenantId != null">
                and p.tenant_id = #{tenantId,jdbcType=INTEGER}
            </if>
        </where>

    </select>

</mapper>