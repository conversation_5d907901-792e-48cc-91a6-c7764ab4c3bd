<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.ProductCategoryInfoMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update product_category_info set has_child = #{status} where id = #{id}
	</update>

	<select id="queryCustomizeItem" parameterType="org.jeecg.modules.business.entity.ProductCategoryInfoBiz" resultType="org.jeecg.modules.business.entity.ProductCategoryInfoBiz">
		select item.id as customizeItemId,
		item.customize_item_name,
		item.item_type,
		item.is_required_items
		from
		product_category_info p
		left join
		product_customize_item_category_info info
		on p.id = info.product_category_info_id
		left join
		product_customize_item_info item
		on info.item_id = item.id
		<where>
			p.del_flag = 0
			and item.del_flag = 0
			and p.id = #{id,jdbcType=VARCHAR}
			<if test="tenantId != null">
				and p.tenant_id = #{tenantId,jdbcType=INTEGER}
			</if>

		</where>

	</select>

	<select id="queryCustomizeItemValue" parameterType="org.jeecg.modules.business.entity.ProductCategoryInfoBiz" resultType="org.jeecg.modules.business.entity.ProductCategoryInfoBiz">
        select item.id as customizeItemId,
        item.customize_item_name,
        item.item_type,
        item.is_required_items,
        goods.item_value,
		goods.id as goodsId,
		goods.update_time as goodsUpdateTime
        from product_category_info p
        left join
        product_customize_item_category_info info
        on p.id = info.product_category_info_id
        left join
        product_customize_item_info item
        on info.item_id = item.id
        left join
        product_customize_item_goods_info goods
        on info.item_id = goods.item_id

		<where>
			p.del_flag = 0
			and item.del_flag = 0
			and p.id = #{id,jdbcType=VARCHAR}
            and goods.product_id = #{productId,jdbcType=VARCHAR}
			<if test="tenantId != null">
				and p.tenant_id = #{tenantId,jdbcType=INTEGER}
			</if>

		</where>

	</select>

	<select id="queryProductInfo" parameterType="org.jeecg.modules.business.entity.ProductCategoryInfoBiz" resultType="java.lang.Integer">
		select count(*) from
		product_info pr
		<where>
			pr.del_flag = 0
			<if test="tenantId != null">
				and pr.tenant_id = #{tenantId,jdbcType=INTEGER}
			</if>
			and pr.product_category_info_id = #{id,jdbcType=VARCHAR}
		</where>

	</select>
</mapper>