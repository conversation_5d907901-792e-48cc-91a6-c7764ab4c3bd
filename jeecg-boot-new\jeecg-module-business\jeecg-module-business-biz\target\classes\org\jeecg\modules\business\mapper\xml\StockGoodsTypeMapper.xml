<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StockGoodsTypeMapper">

    <select id="listStockGoodsByRltWarehousingId"
            resultType="org.jeecg.modules.business.entity.StockGoodsType">
        SELECT
        STOCK_HEAD_TYPE.RLT_WAREHOUSING_ID,STOCK_GOODS_TYPE.*
        FROM
        STOCK_HEAD_TYPE,STOCK_GOODS_TYPE
        WHERE
        STOCK_HEAD_TYPE.ID = STOCK_GOODS_TYPE.STOCK_ID
        AND STOCK_HEAD_TYPE.RLT_WAREHOUSING_ID IN
        <foreach collection="rltWarehousingIds" item="rltWarehousingId" separator="," open="(" close=")">
            #{rltWarehousingId}
        </foreach>
    </select>
    <select id="listStockGoodsType" resultType="org.jeecg.modules.business.entity.StockGoodsType">
        SELECT
            STOCK_HEAD_TYPE.ETPS_PREENT_NO,
            STOCK_HEAD_TYPE.SAS_STOCK_NO,
            STOCK_HEAD_TYPE.PACKAGE_QTY,
            STOCK_HEAD_TYPE.NET_WT AS HEAD_NET_WT,
            STOCK_HEAD_TYPE.GROSS_WT AS HEAD_GROSS_WT,
            STOCK_HEAD_TYPE.CREATE_DATE,
            STOCK_HEAD_TYPE.DECLARATION_DATE,
            STOCK_HEAD_TYPE.RLT_BOND_INVT_NO,
            STOCK_HEAD_TYPE.RLT_INVT_DCL_TIME,
            STOCK_HEAD_TYPE.RLT_ICLEARANCE_NO,
            STOCK_HEAD_TYPE.RLT_IAPP_DATE,
            STOCK_HEAD_TYPE.RLT_ECLEARANCE_NO,
            STOCK_HEAD_TYPE.RLT_EAPP_DATE,
            (SELECT NEMS_INVT_HEAD.ETPS_INNER_INVT_NO FROM NEMS_INVT_HEAD WHERE STOCK_HEAD_TYPE.INVT_HEAD_ID = NEMS_INVT_HEAD.ID) AS ETPS_INNER_INVT_NO,
            STOCK_GOODS_TYPE.*
        FROM
            STOCK_HEAD_TYPE
        LEFT JOIN STOCK_GOODS_TYPE ON STOCK_HEAD_TYPE.ID = STOCK_GOODS_TYPE.STOCK_ID
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ORDER BY CREATE_DATE DESC,STOCK_HEAD_TYPE.ETPS_PREENT_NO,STOCK_GOODS_TYPE.SAS_STOCK_SEQNO
    </select>
</mapper>
