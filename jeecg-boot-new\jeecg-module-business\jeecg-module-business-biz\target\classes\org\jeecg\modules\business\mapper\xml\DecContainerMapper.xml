<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DecContainerMapper">

    <select id="GetListByDecId" resultType="org.jeecg.modules.business.entity.DecContainer">
        SELECT
            *
        FROM
            `dec_container`
        WHERE
            DEC_ID = #{decId}
    </select>

    <select id="listDecContainerPage" resultType="org.jeecg.modules.business.entity.DecContainer">
        SELECT
        dec_container.ID,dec_container.CONTAINER_ID,dec_head.CLEARANCE_NO
        FROM
            `dec_container` inner join dec_head on dec_container.DEC_ID = dec_head.ID
        WHERE
        dec_head.TENANT_ID = #{tenantId}
            <if test="containerId != null and containerId != ''">
                AND dec_container.CONTAINER_ID = #{containerId}
            </if>
        order by dec_head.CREATE_TIME desc
    </select>
</mapper>
