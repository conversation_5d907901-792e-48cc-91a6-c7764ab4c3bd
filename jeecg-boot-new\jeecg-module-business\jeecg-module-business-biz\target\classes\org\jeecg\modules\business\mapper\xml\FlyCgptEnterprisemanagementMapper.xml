<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.FlyCgptEnterprisemanagementMapper">

    <select id="GetMiddleList" parameterType="map" resultType="org.jeecg.modules.business.entity.dto.FlyCgptMiddleResp">
        SELECT
        t0.id,
        t1.danju_type,
        shop_company_name,
        shop_market_no,
        shop_rec_no,
        purchaser_name,
        purchaser_cert_type,
        T2.purchaser_id,
        purchaser_phone,
        T3.shop_account,
        T1.agent_company_name,
        T1.agent_rec_no,
        T1.agent_account
        FROM fly_cgpt_middle AS T0
        LEFT JOIN fly_cgpt_enterprisemanagement AS T1 ON T1.id = T0.manage_id
        LEFT JOIN fly_cgpt_purchaser AS T2 ON T2.id = T0.merchant_id AND T0.TYPE = '1'
        LEFT JOIN fly_cgpt_merchant AS T3 ON T3.id = T0.merchant_id AND T0.TYPE = '0'
        where t0.type=#{type}
        <if test="keyValue!=null">
            and (t1.agent_rec_no=#{keyValue} or t1.Id=#{keyValue})
        </if>
    </select>

</mapper>