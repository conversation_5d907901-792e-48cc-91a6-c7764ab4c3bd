<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.MiniappCompanyIntroductionInfoMapper">
    <select id="getCollectionCompanyIntroList"
            parameterType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo"
            resultType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo">

        SELECT
        id,
        update_time,
        company_picture,
        company_introduction,
        platform_picture,
        remarks
        FROM
        miniapp_company_introduction_info
        <where>
            del_flag = 0
            and tenant_id = 0
        </where>
    </select>
    <select id="getMiniAppPlatformList"
            parameterType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo"
            resultType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo">

        SELECT
        platform_picture
        FROM
        miniapp_company_introduction_info
        <where>
            del_flag = 0
            and tenant_id = 0
        </where>
    </select>
    <select id="getMiniAppCompanyList"
            parameterType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo"
            resultType="org.jeecg.modules.business.entity.MiniappCompanyIntroductionInfo">

        SELECT
        company_picture,
        company_introduction
        FROM
        miniapp_company_introduction_info
        <where>
            del_flag = 0
            and tenant_id = 0
        </where>
    </select>
</mapper>