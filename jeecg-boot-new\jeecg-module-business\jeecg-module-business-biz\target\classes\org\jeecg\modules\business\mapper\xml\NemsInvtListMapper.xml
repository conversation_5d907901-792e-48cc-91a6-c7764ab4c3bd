<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.NemsInvtListMapper">

    <update id="updateBondInvtNoByInvtId">
        UPDATE NEMS_INVT_LIST SET BOND_INVT_NO = #{bondInvtNo} WHERE INV_ID = #{invtId}
    </update>
    <update id="updateListNotPutrecSeqnoById">
        UPDATE NEMS_INVT_LIST
        SET
            SEQ_NO = #{invtList.seqNo},
            GDSSEQ_NO = #{invtList.gdsseqNo},
            GDS_MTNO = #{invtList.gdsMtno},
            HSCODE = #{invtList.hscode},
            HSNAME = #{invtList.hsname},
            HSMODEL = #{invtList.hsmodel},
            DCL_UNITCD = #{invtList.dclUnitcd},
            LAWF_UNITCD = #{invtList.lawfUnitcd},
            SECDLAWF_UNITCD = #{invtList.secdlawfUnitcd},
            NATCD = #{invtList.natcd},
            DCL_UPRCAMT = #{invtList.dclUprcamt},
            DCL_TOTALAMT = #{invtList.dclTotalamt},
            USDSTAT_TOTALAMT = #{invtList.usdstatTotalamt},
            DCL_CURRCD = #{invtList.dclCurrcd},
            LAWF_QTY = #{invtList.lawfQty},
            SECD_LAWF_QTY = #{invtList.secdLawfQty},
            WTSF_VAL = #{invtList.wtsfVal},
            FSTSF_VAL = #{invtList.fstsfVal},
            SECDSF_VAL = #{invtList.secdsfVal},
            DCL_QTY = #{invtList.dclQty},
            GROSS_WT = #{invtList.grossWt},
            NET_WT = #{invtList.netWt},
            USE_CD = #{invtList.useCd},
            LVYRLF_MODECD = #{invtList.lvyrlfModecd},
            UCNS_VERNO = #{invtList.ucnsVerno},
            ENTRY_GDS_SEQNO = #{invtList.entryGdsSeqno},
            CLYMARKCD = #{invtList.clymarkcd},
            APPLY_TB_SEQNO_A = #{invtList.applyTbSeqnoA},
            APPLY_TB_SEQNO_B = #{invtList.applyTbSeqnoB},
            ADD_TIME = #{invtList.addTime},
            ACTL_PASS_QTY = #{invtList.actlPassQty},
            PASS_PORTUSED_QTY = #{invtList.passPortusedQty},
            RMK = #{invtList.rmk},
            HSTYPE = #{invtList.hstype},
            CIQ_CODE = #{invtList.ciqCode},
            CIQ_NAME = #{invtList.ciqName},
            TRANS_MODE = #{invtList.transMode},
            ORIGIN_COUNTRY = #{invtList.originCountry},
            BOND_INVT_NO = #{invtList.bondInvtNo}
        WHERE INV_ID = #{invtList.invId} and

            GDSSEQ_NO = #{invtList.gdsseqNo}
    </update>
    <update id="updatePushStatus">
        UPDATE `PTS_EMS_AIMG` SET PUSH_STATUS = #{pushStatus}
        WHERE
        EMS_NO = #{emsNo} AND G_NO IN
        <foreach collection="gNos" separator="," item="gNo" open="(" close=")">
            #{gNo}
        </foreach>
    </update>

    <select id="listByReportStatistics"  resultType="org.jeecg.modules.business.entity.NemsInvtList">
        select * from nems_invt_list nl inner join nems_invt_head nh
        on nl.INV_ID = nh.ID
        WHERE  DATE_FORMAT(nh.INVT_DCL_TIME, '%Y-%m-%d') between DATE_FORMAT(#{startDate}, '%Y-%m-%d')
                   AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        and nh.SUPV_MODECD!='0245' and nh.IMPEXP_MARKCD = 'I'
        AND nl.GDS_MTNO IN
        <foreach collection="gdsMtnoList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listByReportStatisticsByExport"  resultType="org.jeecg.modules.business.entity.NemsInvtList">
        select * from nems_invt_list nl inner join nems_invt_head nh
        on nl.INV_ID = nh.ID
        WHERE  DATE_FORMAT(nh.INVT_DCL_TIME, '%Y-%m-%d') between DATE_FORMAT(#{startDate}, '%Y-%m-%d')
        AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        and nh.SUPV_MODECD!='0245' and nh.IMPEXP_MARKCD = 'E'
        AND nl.GDS_MTNO IN
        <foreach collection="gdsMtnoList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
