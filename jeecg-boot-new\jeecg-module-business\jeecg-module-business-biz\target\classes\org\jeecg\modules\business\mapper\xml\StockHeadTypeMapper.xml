<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StockHeadTypeMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.StockHeadType">
        SELECT
        STOCK_HEAD_TYPE.*,
        IF(STOCK_HEAD_TYPE.SEQ_NO IS NULL OR STOCK_HEAD_TYPE.SEQ_NO='',
        (
        SELECT edi.NOTE FROM EDI_STATUS_HISTORY edi where STOCK_HEAD_TYPE.ETPS_PREENT_NO = edi.BUSINESS_ID
        AND STOCK_HEAD_TYPE.ETPS_PREENT_NO!=''
        ORDER BY edi.RECEIVER_TIME DESC,edi.PERFORM_SORT DESC,edi.CODE
        LIMIT 1 ),
        (
        SELECT edi.NOTE FROM EDI_STATUS_HISTORY edi where
        STOCK_HEAD_TYPE.SEQ_NO = edi.SEQ_NO AND STOCK_HEAD_TYPE.SEQ_NO!='' ORDER BY edi.RECEIVER_TIME
        DESC,edi.PERFORM_SORT DESC,edi.CODE
        LIMIT 1 )
        ) AS ediNote
        FROM
        STOCK_HEAD_TYPE
        LEFT JOIN STOCK_GOODS_TYPE ON STOCK_HEAD_TYPE.ID = STOCK_GOODS_TYPE.STOCK_ID
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        GROUP BY STOCK_HEAD_TYPE.ID
        ORDER BY CREATE_DATE DESC
    </select>
</mapper>
