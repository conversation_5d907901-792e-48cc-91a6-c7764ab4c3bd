<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.BillingRateMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.BillingRate">
        SELECT
            *
        FROM
            `billing_rate`
        <where>
            <if test="billingRate.rateNo != null and billingRate.rateNo != ''">
                AND RATE_NO LIKE CONCAT('%', #{billingRate.rateNo}, '%')
            </if>
            <if test="billingRate.storeCode != null and billingRate.storeCode != ''">
                AND STORE_CODE = #{billingRate.storeCode}
            </if>
            <if test="billingRate.customer != null and billingRate.customer != ''">
                AND CUSTOMER = #{billingRate.customer}
            </if>
            <if test="billingRate.status != null and billingRate.status != ''">
                AND STATUS = #{billingRate.status}
            </if>
            <if test="billingRate.starSignDate != null and billingRate.starSignDate != ''">
                AND date_format(SIGN_DATE, '%Y-%m-%d') &gt;= #{billingRate.starSignDate}
            </if>
            <if test="billingRate.lastSignDate != null and billingRate.lastSignDate != ''">
                AND date_format(SIGN_DATE, '%Y-%m-%d') &lt;= #{billingRate.lastSignDate}
            </if>
            <if test="billingRate.starEndDate != null and billingRate.starEndDate != ''">
                AND date_format(END_DATE, '%Y-%m-%d') &gt;= #{billingRate.starEndDate}
            </if>
            <if test="billingRate.lastEndDate != null and billingRate.lastEndDate != ''">
                AND date_format(END_DATE, '%Y-%m-%d') &lt;= #{billingRate.lastEndDate}
            </if>
        </where>
        ORDER BY CREATE_DATE DESC
    </select>
</mapper>
