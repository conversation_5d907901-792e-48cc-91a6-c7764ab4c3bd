<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StorageInfoMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.StorageInfo">
        SELECT
            a.*,
            MAX( b.BILL_NO ) billNo,
            MAX( b.PN ) pn,
            MAX( b.DETAIL_TYPE ) detailType
        FROM
            `storage_info` a
                LEFT JOIN storage_detail b ON a.STORAGE_NO = b.STORAGE_NO
        <where>
            a.TENANT_ID = #{storageInfo.tenantId}
            <if test="storageInfo.ieFlag != null and storageInfo.ieFlag != ''">
                AND a.IE_FLAG = #{storageInfo.ieFlag}
            </if>
            <if test="storageInfo.storageNo != null and storageInfo.storageNo != ''">
                AND a.STORAGE_NO LIKE CONCAT('%', #{storageInfo.storageNo}, '%')
            </if>
            <if test="storageInfo.orderNo != null and storageInfo.orderNo != ''">
                AND a.ORDER_NO LIKE CONCAT('%', #{storageInfo.orderNo}, '%')
            </if>
            <if test="storageInfo.status != null and storageInfo.status != ''">
                AND a.STATUS = #{storageInfo.status}
            </if>
            <if test="storageInfo.customer != null and storageInfo.customer != ''">
                AND a.CUSTOMER = #{storageInfo.customer}
            </if>
            <if test="storageInfo.storeCode != null and storageInfo.storeCode != ''">
                AND a.STORE_CODE = #{storageInfo.storeCode}
            </if>
            <if test="storageInfo.paymentMethod != null and storageInfo.paymentMethod != ''">
                AND a.PAYMENT_METHOD = #{storageInfo.paymentMethod}
            </if>
            <if test="storageInfo.appDate != null">
                AND date_format(a.APP_DATE, '%Y-%m-%d') = date_format(#{storageInfo.appDate}, '%Y-%m-%d')
            </if>
            <if test="storageInfo.billNo != null and storageInfo.billNo != ''">
                AND b.BILL_NO LIKE CONCAT('%', #{storageInfo.billNo}, '%')
            </if>
            <if test="storageInfo.pn != null and storageInfo.pn != ''">
                AND b.PN LIKE CONCAT('%', #{storageInfo.pn}, '%')
            </if>
            <if test="storageInfo.detailType != null and storageInfo.detailType != ''">
                AND b.DETAIL_TYPE = #{storageInfo.detailType}
            </if>
            <choose>
                <when test="storageInfo.isReturn != null and storageInfo.isReturn != ''">
                    AND a.IS_RETURN = #{storageInfo.isReturn}
                </when>
                <otherwise>
                    AND (a.IS_RETURN IS NULL OR a.IS_RETURN = '' OR a.IS_RETURN = '0')
                </otherwise>
            </choose>
        </where>
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>

    <select id="bondedWarehouseInDetailList"
            resultType="org.jeecg.modules.business.entity.dto.InOrOutStorageDetailDTO">
        SELECT
            a.ID,
            a.STORAGE_NO,
            ANY_VALUE ( b.APP_DATE ) appDate,
            ANY_VALUE ( b.IE_FLAG ) ieFlag,
            ANY_VALUE ( b.CUSTOMER ) customer,
            ANY_VALUE ( c.GDSSEQ_NO ) gdsseqNo,
            a.COP_GNO,
            ANY_VALUE (b.REMARK) remark,
            ANY_VALUE ( IFNULL( c.PUTREC_SEQNO, a.ITEM_NUMBER ) ) putrecSeqno,
            ANY_VALUE ( IFNULL( f.CODET, a.COP_GNO ) ) codet,
            ANY_VALUE ( IFNULL( f.G_NAME, a.PN ) ) gName,
            ANY_VALUE ( IFNULL( f.G_MODEL, a.MODEL ) ) gModel,
            a.ORIGIN_COUNTRY countryCode,
            ANY_VALUE ( f.UNIT ) unit,
            a.ACTUAL_QTY,
            a.TOTAL_PRICE_TAX amount,
            a.BOND_INVT_NO  bondInvtNo,
            a.SPACE_CODE,
            a.SUPV_MODECD  tradeTypeCode,
            a.SHIP_NAME  shipName,
            a.VOYAGE  voyage,
            a.BATCH_NO,
            a.DETAIL_TYPE,
            a.SN,
            a.BILL_NO,
            a.HSCODE hscode,
            a.ITEM_NUMBER,
            a.SUPV_MODECD,
            a.QUNIT,
            a.STORE_CODE,
            a.AREA_CODE,
            a.NET_WEIGHT
        FROM
            `storage_detail` a
            LEFT JOIN storage_info b ON a.STORAGE_NO = b.STORAGE_NO
            LEFT JOIN nems_invt_list c ON c.STORAGE_DETAIL_ID = a.ID
            LEFT JOIN nems_invt_head d ON d.ID = c.INV_ID
            LEFT JOIN pts_ems_head e ON e.EMS_NO = d.PUTREC_NO
            LEFT JOIN pts_ems_aimg f ON e.ID = f.EMS_ID
            AND c.PUTREC_SEQNO = f.G_NO
        <where>
                a.TENANT_ID = #{inOrOutStorageDetailDTO.tenantId}
            <if test="inOrOutStorageDetailDTO.customer != null and inOrOutStorageDetailDTO.customer != ''">
                AND b.CUSTOMER = #{inOrOutStorageDetailDTO.customer}
            </if>
            <if test="inOrOutStorageDetailDTO.storageNo != null and inOrOutStorageDetailDTO.storageNo != ''">
                AND a.STORAGE_NO LIKE CONCAT('%', #{inOrOutStorageDetailDTO.storageNo}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.copGno != null and inOrOutStorageDetailDTO.copGno != ''">
                AND a.COP_GNO = #{inOrOutStorageDetailDTO.copGno}
            </if>
            <if test="inOrOutStorageDetailDTO.ieFlag != null and inOrOutStorageDetailDTO.ieFlag != ''">
                AND b.IE_FLAG = #{inOrOutStorageDetailDTO.ieFlag}
            </if>
            <if test="inOrOutStorageDetailDTO.putrecSeqno != null and inOrOutStorageDetailDTO.putrecSeqno != ''">
                AND (c.PUTREC_SEQNO LIKE CONCAT('%', #{inOrOutStorageDetailDTO.putrecSeqno}, '%') OR a.ITEM_NUMBER LIKE CONCAT('%', #{inOrOutStorageDetailDTO.putrecSeqno}, '%'))
            </if>
            <if test="inOrOutStorageDetailDTO.batchNo != null and inOrOutStorageDetailDTO.batchNo != ''">
                AND a.BATCH_NO LIKE CONCAT('%', #{inOrOutStorageDetailDTO.batchNo}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.gName != null and inOrOutStorageDetailDTO.gName != ''">
                AND a.PN LIKE CONCAT('%', #{inOrOutStorageDetailDTO.gName}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.sn != null and inOrOutStorageDetailDTO.sn != ''">
                AND a.SN LIKE CONCAT('%', #{inOrOutStorageDetailDTO.sn}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.billNo != null and inOrOutStorageDetailDTO.billNo != ''">
                AND a.BILL_NO LIKE CONCAT('%', #{inOrOutStorageDetailDTO.billNo}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.detailType != null and inOrOutStorageDetailDTO.detailType != ''">
                AND a.DETAIL_TYPE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.detailType}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.appDateStart != null and inOrOutStorageDetailDTO.appDateStart != ''">
                AND date_format(b.APP_DATE, '%Y-%m-%d') &gt;= #{inOrOutStorageDetailDTO.appDateStart}
            </if>
            <if test="inOrOutStorageDetailDTO.appDateEnd != null and inOrOutStorageDetailDTO.appDateEnd != ''">
                AND date_format(b.APP_DATE, '%Y-%m-%d') &lt;= #{inOrOutStorageDetailDTO.appDateEnd}
            </if>
            <if test="inOrOutStorageDetailDTO.hscode != null and inOrOutStorageDetailDTO.hscode != ''">
                AND a.HSCODE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.hscode}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.spaceCode != null and inOrOutStorageDetailDTO.spaceCode != ''">
                AND a.SPACE_CODE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.spaceCode}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.bondInvtNo != null and inOrOutStorageDetailDTO.bondInvtNo != ''">
                AND a.BOND_INVT_NO LIKE CONCAT('%', #{inOrOutStorageDetailDTO.bondInvtNo}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.shipName != null and inOrOutStorageDetailDTO.shipName != ''">
                AND a.SHIP_NAME LIKE CONCAT('%', #{inOrOutStorageDetailDTO.shipName}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.voyage != null and inOrOutStorageDetailDTO.voyage != ''">
                AND a.VOYAGE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.voyage}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.supvModecd != null and inOrOutStorageDetailDTO.supvModecd != ''">
                AND a.SUPV_MODECD = #{inOrOutStorageDetailDTO.supvModecd}
            </if>
            <if test="inOrOutStorageDetailDTO.storeCode != null and inOrOutStorageDetailDTO.storeCode != ''">
                AND a.STORE_CODE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.storeCode}, '%')
            </if>
            <if test="inOrOutStorageDetailDTO.areaCode != null and inOrOutStorageDetailDTO.areaCode != ''">
                AND a.AREA_CODE LIKE CONCAT('%', #{inOrOutStorageDetailDTO.areaCode}, '%')
            </if>
        </where>
        GROUP BY
            a.ID,
            b.CREATE_DATE
        ORDER BY
            b.CREATE_DATE DESC
    </select>
    <select id="listForCreateInvt" resultType="org.jeecg.modules.business.entity.StorageInfo">
        SELECT
            a.*
        FROM
            `storage_info` a
                LEFT JOIN storage_detail b ON a.STORAGE_NO = b.STORAGE_NO
        <where>
            a.IE_FLAG = 'E'
            AND (b.INVT_LIST_ID = '' OR b.INVT_LIST_ID IS NULL)
            AND (a.STATUS = '1')
            <if test="storageInfo.storageNo != null and storageInfo.storageNo != ''">
                AND a.STORAGE_NO LIKE CONCAT('%', #{storageInfo.storageNo}, '%')
            </if>
            <if test="storageInfo.customer != null and storageInfo.customer != ''">
                AND a.CUSTOMER = #{storageInfo.customer}
            </if>
            <if test="storageInfo.storeCode != null and storageInfo.storeCode != ''">
                AND a.STORE_CODE = #{storageInfo.storeCode}
            </if>
            <if test="storageInfo.paymentMethod != null and storageInfo.paymentMethod != ''">
                AND a.PAYMENT_METHOD = #{storageInfo.paymentMethod}
            </if>
        </where>
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>
    <select id="getStorageInfoByDateRange" resultType="org.jeecg.modules.business.entity.StorageInfo">
        SELECT
            *
        FROM
            `storage_info`
        WHERE
            IE_FLAG = 'I'
          AND `TYPE` = '1'
    </select>
    <select id="getStorageDetailByCond" resultType="org.jeecg.modules.business.entity.StorageDetail">
        SELECT
            a.*
        FROM
            `storage_detail` a
                LEFT JOIN storage_info b ON a.STORAGE_NO = b.STORAGE_NO
        WHERE
            b.`TYPE` = 1
          AND b.IE_FLAG = #{ieFlag}
          AND a.COP_GNO = #{copGno}
          AND a.DETAIL_TYPE = #{detailType}
          AND a.TENANT_ID = #{tenantId}
    </select>

    <select id="listInvtByGenerateStorage" resultType="org.jeecg.modules.business.entity.NemsInvtHead">
        SELECT
            a.*
        FROM
            `nems_invt_head` a
                LEFT JOIN nems_invt_list b ON a.ID = b.INV_ID
        where a.VRFDED_MARKCD = '2'
          AND a.DCLCUS_FLAG = '1'
        AND (
            b.STORAGE_NO = ''
                OR b.STORAGE_NO IS NULL)
        AND a.TENANT_ID = #{tenantId}
        AND a.INVT_DCL_TIME &gt;= #{startDate}
          AND a.INVT_DCL_TIME &lt;= #{endDate}
    </select>
</mapper>
