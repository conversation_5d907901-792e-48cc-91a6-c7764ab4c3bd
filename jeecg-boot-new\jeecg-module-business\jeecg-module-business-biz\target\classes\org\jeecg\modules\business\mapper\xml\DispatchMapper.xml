<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DispatchMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.Dispatch">
        SELECT
            a.*
        FROM
            `dispatch` a
        <where>
            <if test="dispatch.shipType != null and dispatch.shipType != ''">
                AND a.SHIP_TYPE = #{dispatch.shipType}
            </if>
            <if test="dispatch.imSign != null and dispatch.imSign != ''">
                AND a.IM_SIGN = #{dispatch.imSign}
            </if>
            <if test="dispatch.containerLoad != null and dispatch.containerLoad != ''">
                AND a.CONTAINER_LOAD = #{dispatch.containerLoad}
            </if>
            <if test="dispatch.dispatchNo != null and dispatch.dispatchNo != ''">
                AND a.DISPATCH_NO LIKE CONCAT('%', #{dispatch.dispatchNo}, '%')
            </if>
            <if test="dispatch.waybillNo != null and dispatch.waybillNo != ''">
                AND a.WAYBILL_NO LIKE CONCAT('%', #{dispatch.waybillNo}, '%')
            </if>
            <if test="dispatch.consignorName != null and dispatch.consignorName != ''">
                AND a.CONSIGNOR_NAME LIKE CONCAT('%', #{dispatch.consignorName}, '%')
            </if>
            <if test="dispatch.gmtUseCarStart != null and dispatch.gmtUseCarStart !=''
             and dispatch.gmtUseCarEnd != null and dispatch.gmtUseCarEnd !='' ">
                AND date_format(a.GMT_USE_CAR, '%Y-%m-%d')  between date_format(#{dispatch.gmtUseCarStart}, '%Y-%m-%d')
                and date_format(#{dispatch.gmtUseCarEnd}, '%Y-%m-%d')
            </if>
            <if test="dispatch.warehousingNumber != null and dispatch.warehousingNumber != ''">
                AND a.WAREHOUSING_NUMBER LIKE CONCAT('%', #{dispatch.warehousingNumber}, '%')
            </if>
            <if test="dispatch.status != null and dispatch.status != ''">
                AND a.STATUS = #{dispatch.status}
            </if>
            <if test="dispatch.carDispatcher != null and dispatch.carDispatcher != ''">
                AND a.CAR_DISPATCHER LIKE CONCAT('%', #{dispatch.carDispatcher}, '%')
            </if>
            <if test="dispatch.transportRouteStart != null and dispatch.transportRouteStart != ''">
                AND a.TRANSPORT_ROUTE_START LIKE CONCAT('%', #{dispatch.transportRouteStart}, '%')
            </if>
            <if test="dispatch.transportRouteEnd != null and dispatch.transportRouteEnd != ''">
                AND a.TRANSPORT_ROUTE_END LIKE CONCAT('%', #{dispatch.transportRouteEnd}, '%')
            </if>
            <if test="dispatch.carrierFleetName != null and dispatch.carrierFleetName != ''">
                AND a.CARRIER_FLEET_NAME LIKE CONCAT('%', #{dispatch.carrierFleetName}, '%')
            </if>
            <if test="dispatch.goodsName != null and dispatch.goodsName != ''">
                AND a.GOODS_NAME LIKE CONCAT('%', #{dispatch.goodsName}, '%')
            </if>
            <if test="dispatch.contracts != null and dispatch.contracts != ''">
                AND a.CONTRACTS LIKE CONCAT('%', #{dispatch.contracts}, '%')
            </if>
        </where>
        ORDER BY
            a.CREATE_DATE DESC
    </select>
    <select id="sumCostByTransportIds" resultType="java.math.BigDecimal">
        select  sum(COST_VALUE) from DISPATCH_TRANSPORT_COST
        <where>
            AND TRANSPORT_ID IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="sumYsCostByTransportIds" resultType="java.math.BigDecimal">
        select  sum(REAL_AMOUNT) from DISPATCH_TRANSPORT_COST
        <where>
            AND TRANSPORT_ID IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>
