<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.OrderSummaryInfoMapper">
<!--    查询订单基本信息-->
    <select id="getOrderList" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz"
            resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
        SELECT
        o.id,
        o.tenant_id,
        o.create_by,
        o.create_time,
        o.update_time,
        o.order_protocol_no,
        o.order_no_nine_seven,
        o.order_stock_no,
        o.supervision_mode,
        o.trading_country,
        o.overseas_payer_info_id,
        o.customs_declaration_currency,
        o.exchange_collection_type,
        o.total_contract_amount,
        o.invoice_no,
        o.export_contract_no,
        o.customer_service_manager,
        o.sales_manager,
        o.customs_number,
        o.delivery_numbers,
        o.estimated_shipment_date,
        o.receiver,
        o.exit_clearance,
        o.freight_amount,
        o.premium_amount,
        o.miscellaneous_amount,
        o.domestic_source_of_goods,
        o.final_contry,
        o.trading_type,
        o.customs_broker_id,
        o.close_date,
        o.overseas_warehouse_name,
        o.overseas_warehouse_address,
        o.overseas_warehouse_country,
        o.overseas_warehouse_platform,
        o.foreign_exchange_amount,
        o.order_status,
        o.receive_money_flag,
        o.pay_money_flag,
        o.has_pay_money_flag,
        o.logistics_service_mode,
        o.del_flag,
        o.remarks,
        o.remind_status,
        o.GUESS_RATE
        FROM
        order_info o
        WHERE
        o.del_flag = 0
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and o.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 监管模式 -->
        <if test="orderSummaryInfoBiz.supervisionMode != null">
            and o.supervision_mode = #{orderSummaryInfoBiz.supervisionMode,jdbcType=INTEGER}
        </if>
        <!-- 贸易国（地区） -->
        <if test="orderSummaryInfoBiz.tradingCountry != null">
            and o.trading_country = #{orderSummaryInfoBiz.tradingCountry,jdbcType=VARCHAR}
        </if>
        <!-- 报关单号 -->
        <if test="orderSummaryInfoBiz.customsNumber != null">
            and o.customs_number = #{orderSummaryInfoBiz.customsNumber,jdbcType=VARCHAR}
        </if>
        <!-- 订单状态 -->
        <if test="orderSummaryInfoBiz.orderStatus != null and orderSummaryInfoBiz.orderStatus == 1">
            and o.order_status in (1,2)
        </if>
        <if test="orderSummaryInfoBiz.orderStatus != null and orderSummaryInfoBiz.orderStatus == 99">
            and o.order_status in (3,9)
        </if>
        <if test="orderSummaryInfoBiz.orderStatus != null and orderSummaryInfoBiz.orderStatus != 1 and orderSummaryInfoBiz.orderStatus != 99 ">
            and o.order_status = #{orderSummaryInfoBiz.orderStatus,jdbcType=INTEGER}
        </if>
        <!-- 外汇收齐状态 -->
        <if test="orderSummaryInfoBiz.receiveMoneyFlag != null">
            and o.receive_money_flag = #{orderSummaryInfoBiz.receiveMoneyFlag,jdbcType=INTEGER}
        </if>
        <!-- 外汇转款状态 -->
        <if test="orderSummaryInfoBiz.payMoneyFlag != null">
            and o.pay_money_flag = #{orderSummaryInfoBiz.payMoneyFlag,jdbcType=INTEGER}
        </if>
        <!-- 境外付款方 -->
        <if test="orderSummaryInfoBiz.overseasPayerInfoId != null">
            and o.overseas_payer_info_id = #{orderSummaryInfoBiz.overseasPayerInfoId,jdbcType=VARCHAR}
        </if>
        <!-- 首页提醒用 提醒状态 -->
        <if test="orderSummaryInfoBiz.remindStatus != null">
            and o.remind_status != 0
        </if>
        <!-- 进出口标识 -->
        <if test="orderSummaryInfoBiz.ieFlag != null">
            and o.IE_FLAG = #{orderSummaryInfoBiz.ieFlag,jdbcType=VARCHAR}
        </if>
        <!-- 订单印刷用 区间 -->
        <if test="orderSummaryInfoBiz.startDate != null and orderSummaryInfoBiz.endDate != null">
            and date_format(o.close_date, '%Y-%m-%d') &gt;= #{orderSummaryInfoBiz.startDate}
            and date_format(o.close_date, '%Y-%m-%d') &lt;= #{orderSummaryInfoBiz.endDate}
        ORDER BY
        o.close_date DESC
        </if>
        <if test="orderSummaryInfoBiz.startDate == null and orderSummaryInfoBiz.endDate == null">
            ORDER BY
            o.remind_status DESC,
            o.create_time DESC
        </if>
    </select>

    <select id="listPayMentDetailByQuery" parameterType="org.jeecg.modules.business.vo.OrderDaiLiFeiExcelVO"
            resultType="org.jeecg.modules.business.vo.OrderDaiLiFeiExcelVO">
        SELECT
               o.CLOSE_DATE,
            o.ORDER_PROTOCOL_NO,
            f.FEE_NAME,
            opd.PAYMENT_AMOUNT,
            opd.PAYMENT_CURRENCY
        FROM
            ORDER_INFO o
                INNER JOIN ORDER_PAYMENT_INFO op ON o.ID = op.ORDER_INFO_ID
                INNER JOIN ORDER_PAYMENT_DETAIL_INFO opd ON opd.ORDER_PAYMENT_INFO_ID = op.ID
                INNER JOIN FEE_ITEM f ON f.ID = opd.PAYMENT_ITEM
        WHERE
            o.TENANT_ID = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
          AND o.DEL_FLAG = 0
          AND o.RECEIVE_MONEY_FLAG = 1
          AND o.PAY_MONEY_FLAG = 1
          AND o.order_status IN ( 3, 9 )
          AND o.IE_FLAG = 'E'
          AND op.PAYEE_TYPE IN ( 3, 2 )
        <if test="orderSummaryInfoBiz.startDate != null and orderSummaryInfoBiz.endDate != null">
            and o.close_date >= #{orderSummaryInfoBiz.startDate,jdbcType=VARCHAR}
            and o.close_date <![CDATA[ <= ]]> #{orderSummaryInfoBiz.endDate,jdbcType=VARCHAR}
            ORDER BY
            o.close_date DESC
        </if>
        <if test="orderSummaryInfoBiz.startDate == null and orderSummaryInfoBiz.endDate == null">
            ORDER BY
            o.create_time DESC
        </if>
    </select>


    <!--    查询订单基本信息（打印）-->
    <select id="getOrderListXls" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz"
            resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
    SELECT
    o.id,
    o.tenant_id,
    o.create_by,
    o.create_time,
    o.update_time,
    o.order_protocol_no,
    o.order_no_nine_seven,
    o.order_stock_no,
    o.supervision_mode,
    o.trading_country,
    o.overseas_payer_info_id,
    o.customs_declaration_currency,
    o.exchange_collection_type,
    o.total_contract_amount,
    o.invoice_no,
    o.export_contract_no,
    o.customer_service_manager,
    o.sales_manager,
    o.customs_number,
    o.delivery_numbers,
    o.estimated_shipment_date,
    o.receiver,
    o.exit_clearance,
    o.freight_amount,
    o.premium_amount,
    o.miscellaneous_amount,
    o.domestic_source_of_goods,
    o.final_contry,
    o.trading_type,
    o.customs_broker_id,
    o.close_date,
    o.overseas_warehouse_name,
    o.overseas_warehouse_address,
    o.overseas_warehouse_country,
    o.overseas_warehouse_platform,
    o.foreign_exchange_amount,
    o.order_status,
    o.receive_money_flag,
    o.pay_money_flag,
    o.has_pay_money_flag,
    o.logistics_service_mode,
    o.del_flag,
    o.remarks,
    o.remind_status,
    o.GUESS_RATE
    FROM
    order_info o
    WHERE
    o.del_flag = 0
    <!-- 租户id -->
    <if test="tenantId != null">
        and o.tenant_id = #{tenantId,jdbcType=INTEGER}
    </if>
    <!-- 监管模式 -->
    <if test="supervisionMode != null">
        and o.supervision_mode = #{supervisionMode,jdbcType=INTEGER}
    </if>
    <!-- 贸易国（地区） -->
    <if test="tradingCountry != null">
        and o.trading_country = #{tradingCountry,jdbcType=VARCHAR}
    </if>
    <!-- 报关单号 -->
    <if test="customsNumber != null">
        and o.customs_number = #{customsNumber,jdbcType=VARCHAR}
    </if>
    <!-- 订单状态 -->
    <if test="orderStatus != null and orderStatus == 1">
        and o.order_status in (3,9)
    </if>
    <if test="orderStatus != null and orderStatus != 1">
        and o.order_status = #{orderStatus,jdbcType=INTEGER}
    </if>
    <!-- 外汇收齐状态 -->
    <if test="receiveMoneyFlag != null">
        and o.receive_money_flag = #{receiveMoneyFlag,jdbcType=INTEGER}
    </if>
    <!-- 外汇转款状态 -->
    <if test="payMoneyFlag != null">
        and o.pay_money_flag = #{payMoneyFlag,jdbcType=INTEGER}
    </if>
    <!-- 境外付款方 -->
    <if test="overseasPayerInfoId != null">
        and o.overseas_payer_info_id = #{overseasPayerInfoId,jdbcType=VARCHAR}
    </if>
    <!-- 首页提醒用 提醒状态 -->
    <if test="remindStatus != null">
        and o.remind_status != 0
    </if>
    <!-- 进出口标识 -->
    <if test="ieFlag != null">
        and o.IE_FLAG = #{ieFlag,jdbcType=VARCHAR}
    </if>
    <!-- 订单印刷用 区间 -->
    <if test="startDate != null and endDate != null">
        and o.close_date >= #{startDate,jdbcType=VARCHAR}
        and o.close_date <![CDATA[ <= ]]> #{endDate,jdbcType=VARCHAR}
        ORDER BY
        o.close_date DESC
    </if>
    <if test="startDate == null and endDate == null">
        ORDER BY
        o.remind_status DESC,
        o.create_time DESC
    </if>
    </select>

    <!--    查询订单收入明细（打印）-->
    <select id="getOrderPrintInAmountXlsList" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz" resultType="HashMap">
        SELECT
        DATE_FORMAT(f.collection_date, '%Y-%m-%d') AS collectionDate,
        his.collection_matching_amount AS collectionMatchingAmount,
        erp.currency,
        f.rate,
        f.rate * his.collection_matching_amount AS amount
        FROM
        foreign_exchange_collection_info f
        LEFT JOIN matching_history_info his ON f.id = his.foreign_exchange_collection_id
        AND his.collection_matching_amount != 0
        LEFT JOIN erp_currencies erp ON erp.CODE = f.import_currency_type
        WHERE
        f.del_flag = 0
        <!-- 租户id -->
        <if test="tenantId != null">
            and f.tenant_id = #{tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单id -->
        <if test="id != null">
            and his.order_info_id = #{id,jdbcType=VARCHAR}
        </if>
        ORDER BY F.collection_date
    </select>

    <!--    查询订单支出明细（打印）-->
    <select id="getOrderPrintOutAmountXlsList" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz" resultType="HashMap">
        SELECT
        DATE_FORMAT(info.actual_payment_date, '%Y-%m-%d') AS actualPaymentDate,
        CASE

        WHEN info.payee_type = 1 THEN
        '货款'
        WHEN info.payee_type = 2 THEN
        '报关行'
        WHEN info.payee_type = 3 THEN
        '货代公司'
        WHEN info.payee_type = 4 THEN
        '付平台'
        WHEN info.payee_type = 5 THEN
        '其他'
        WHEN info.payee_type = 6 THEN
        '佣金' ELSE f.fee_name
        END AS feeName,
        info.payee_name AS payeeName,
        CASE

        WHEN d.payment_amount IS NULL THEN
        info.payment_amount ELSE d.payment_amount
        END AS paymentAmount
        FROM
        order_payment_info info
        LEFT JOIN order_payment_detail_info d ON info.id = d.order_payment_info_id
        AND d.del_flag = 0
        LEFT JOIN fee_item f ON f.id = d.payment_item
        AND d.tenant_id = f.tenant_id
        WHERE
        info.del_flag = 0
        <!-- 租户id -->
        <if test="tenantId != null">
            and info.tenant_id = #{tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单id -->
        <if test="id != null">
            and info.order_info_id = #{id,jdbcType=VARCHAR}
        </if>
        ORDER BY
        info.actual_payment_date
    </select>

    <!--    查询订单收入-->
    <select id="getOrderIncome" parameterType="String" resultType="BigDecimal">
        SELECT
        CASE
        WHEN
        sum( his.matching_amount * f.rate ) IS NULL THEN
        0 ELSE sum( his.matching_amount * f.rate )
        END AS amount
        FROM
        order_info info
        LEFT JOIN matching_history_info his ON his.order_info_id = info.id
        LEFT JOIN foreign_exchange_collection_info f ON f.id = his.foreign_exchange_collection_id
        WHERE
        info.id = #{id,jdbcType=VARCHAR}
    </select>
    <!--    查询订单支出-->
    <select id="getOrderOut" parameterType="String" resultType="BigDecimal">
        SELECT
        CASE
        WHEN
        sum( pay.payment_amount ) IS NULL THEN
        0 ELSE sum( pay.payment_amount )
        END AS amount
        FROM
        order_info info
        LEFT JOIN order_payment_info pay ON pay.order_info_id = info.id
        AND pay.del_flag = 0
        AND payment_status =1
        WHERE
        info.id = #{id,jdbcType=VARCHAR}
    </select>
    <!--    查询订单收入明细-->
    <select id="getOrderPrintInAmountList" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz" resultType="HashMap">
        SELECT
        DATE_FORMAT(f.collection_date, '%Y-%m-%d') AS collectionDate,
        his.collection_matching_amount AS collectionMatchingAmount,
        erp.currency,
        f.rate,
        f.rate * his.collection_matching_amount AS amount
        FROM
        foreign_exchange_collection_info f
        LEFT JOIN matching_history_info his ON f.id = his.foreign_exchange_collection_id
        AND his.collection_matching_amount != 0
        LEFT JOIN erp_currencies erp ON erp.CODE = f.import_currency_type
        WHERE
        f.del_flag = 0
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and f.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单id -->
        <if test="orderSummaryInfoBiz.id != null">
            and his.order_info_id = #{orderSummaryInfoBiz.id,jdbcType=VARCHAR}
        </if>
        ORDER BY F.collection_date
    </select>
    <!--    查询订单支出明细-->
    <select id="getOrderPrintOutAmountList" parameterType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz" resultType="HashMap">
        SELECT
        DATE_FORMAT(info.actual_payment_date, '%Y-%m-%d') AS actualPaymentDate,
        CASE

        WHEN info.payee_type = 1 THEN
        '货款'
        WHEN info.payee_type = 6 THEN
        '佣金' ELSE '费用'
        END AS payeeTypeText,
        CASE

        WHEN info.payee_type = 1 THEN
        '货款'
        WHEN info.payee_type = 6 THEN
        '佣金' ELSE ( CASE WHEN f.fee_name IS NULL THEN '费用' ELSE f.fee_name END )
        END AS feeName,
        info.payee_name AS payeeName,
        CASE

        WHEN d.payment_amount IS NULL THEN
        info.payment_amount ELSE d.payment_amount
        END AS paymentAmount
        FROM
        order_payment_info info
        LEFT JOIN order_payment_detail_info d ON info.id = d.order_payment_info_id
        AND d.del_flag = 0
        LEFT JOIN fee_item f ON f.id = d.payment_item
        AND d.tenant_id = f.tenant_id
        WHERE
        info.del_flag = 0
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and info.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单id -->
        <if test="orderSummaryInfoBiz.id != null">
            and info.order_info_id = #{orderSummaryInfoBiz.id,jdbcType=VARCHAR}
        </if>
        ORDER BY
        info.actual_payment_date
    </select>
    <!--    查询订单支出-->
    <select id="getAgencyFee" parameterType="String" resultType="BigDecimal">
        SELECT
        CASE

        WHEN
        sum( info.payment_amount ) IS NULL THEN
        0 ELSE sum( info.payment_amount )
        END AS amount
        FROM
        order_payment_info info
        WHERE
        info.order_info_id = #{id,jdbcType=VARCHAR}
        AND info.payee_type = 4
    </select>

    <select id="getOverseasOrderStatistics" parameterType="org.jeecg.modules.business.vo.OverseasOrderStatisticsVO" resultType="org.jeecg.modules.business.vo.OverseasOrderStatisticsVO">
        SELECT
            payer.ID AS OVERSEAS_PAYER_INFO_ID,
            COUNT( orderInfo.ID ) AS ORDER_SUM
        FROM
            OVERSEAS_PAYER_INFO payer
                LEFT JOIN ORDER_INFO orderInfo ON orderInfo.OVERSEAS_PAYER_INFO_ID = payer.ID
        <where>
            payer.DEL_FLAG = '0'
            AND orderInfo.DEL_FLAG = '0'
            <if test="overseasOrderStatisticsVO.tenantId != null">
                and orderInfo.tenant_id = #{overseasOrderStatisticsVO.tenantId,jdbcType=INTEGER}
            </if>
            <if test="overseasOrderStatisticsVO.overseasPayerName != null and overseasOrderStatisticsVO.overseasPayerName != ''">
                AND payer.OVERSEAS_PAYER_NAME LIKE #{overseasOrderStatisticsVO.overseasPayerName}
            </if>
            <if test="overseasOrderStatisticsVO.queryTimeStart != null and overseasOrderStatisticsVO.queryTimeStart != ''
            and overseasOrderStatisticsVO.queryTimeEnd != null and overseasOrderStatisticsVO.queryTimeEnd != ''">
                AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &gt;= #{overseasOrderStatisticsVO.queryTimeStart}
                AND DATE_FORMAT(orderInfo.CREATE_TIME, '%Y-%m-%d') &lt;= #{overseasOrderStatisticsVO.queryTimeEnd}
            </if>
        </where>
        GROUP BY payer.ID
    </select>
    <select id="getOrderStatistics" parameterType="org.jeecg.modules.business.vo.OrderStatisticsVO"
            resultType="org.jeecg.modules.business.vo.OrderStatisticsVO">
        SELECT ORDER_INFO.ORDER_STATUS,ORDER_INFO.IE_FLAG,COUNT( ORDER_INFO.ID ) AS ORDER_SUM
        FROM ORDER_INFO
        <where>
        and ORDER_INFO.DEL_FLAG = 0
            <if test="orderStatisticsVO.tenantId != null">
                and ORDER_INFO.tenant_id = #{orderStatisticsVO.tenantId,jdbcType=INTEGER}
            </if>
            <if test="orderStatisticsVO.orderStatus != null and orderStatisticsVO.orderStatus != ''">
                AND ORDER_INFO.ORDER_STATUS = #{orderStatisticsVO.orderStatus}
            </if>
            <if test="orderStatisticsVO.ieFlag != null and orderStatisticsVO.ieFlag != ''">
                AND ORDER_INFO.IE_FLAG = #{orderStatisticsVO.ieFlag}
            </if>
            <if test="orderStatisticsVO.queryTimeStart != null and orderStatisticsVO.queryTimeStart != ''
            and orderStatisticsVO.queryTimeEnd != null and orderStatisticsVO.queryTimeEnd != ''">
                AND DATE_FORMAT(ORDER_INFO.CREATE_TIME, '%Y-%m-%d') &gt;= #{orderStatisticsVO.queryTimeStart}
                AND DATE_FORMAT(ORDER_INFO.CREATE_TIME, '%Y-%m-%d') &lt;= #{orderStatisticsVO.queryTimeEnd}
            </if>
        </where>
        GROUP BY ORDER_INFO.IE_FLAG,ORDER_INFO.ORDER_STATUS
        ORDER BY ORDER_INFO.ORDER_STATUS
    </select>
    <select id="queryOrderTHBStatistics" parameterType="org.jeecg.modules.business.vo.OrderStatisticsVO"
            resultType="org.jeecg.modules.business.vo.OrderStatisticsVO">
        SELECT
        DATE_FORMAT(CREATE_TIME, '%Y-%m') AS CREATE_TIME,
        COUNT( ID ) AS ORDER_SUM
        FROM ORDER_INFO
        <where>
             and ORDER_INFO.ORDER_STATUS IN(3,9)
            and ORDER_INFO.DEL_FLAG = 0
            <if test="orderStatisticsVO.tenantId != null">
                and ORDER_INFO.tenant_id = #{orderStatisticsVO.tenantId,jdbcType=INTEGER}
            </if>
            <if test="orderStatisticsVO.queryTimeStart != null and orderStatisticsVO.queryTimeStart != ''
            and orderStatisticsVO.queryTimeEnd != null and orderStatisticsVO.queryTimeEnd != ''">
                AND DATE_FORMAT(ORDER_INFO.CREATE_TIME, '%Y-%m-%d') &gt;= #{orderStatisticsVO.queryTimeStart}
                AND DATE_FORMAT(ORDER_INFO.CREATE_TIME, '%Y-%m-%d') &lt;= #{orderStatisticsVO.queryTimeEnd}
            </if>
        </where>
        GROUP BY DATE_FORMAT(CREATE_TIME, '%Y-%m')
        ORDER BY CREATE_TIME DESC
    </select>



    <select id="queryPageListOrderRisksWarning"
            resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
        SELECT
            MAX(o.id) AS id,
            MAX(o.ie_flag) AS ie_flag,
            MAX(o.create_by) AS create_by,
            MAX(o.create_time) AS create_time,
            MAX(o.update_time) AS update_time,
            MAX(o.order_protocol_no) AS order_protocol_no,
            MAX(o.order_no_nine_seven) AS order_no_nine_seven,
            MAX(o.order_stock_no) AS order_stock_no,
            MAX(o.supervision_mode) AS supervision_mode,
            MAX(o.trading_country) AS trading_country,
            MAX(o.overseas_payer_info_id) AS overseas_payer_info_id,
            MAX(o.customs_declaration_currency) AS customs_declaration_currency,
            MAX(o.exchange_collection_type) AS exchange_collection_type,
            MAX(o.total_contract_amount) AS total_contract_amount,
            MAX(o.invoice_no) AS invoice_no,
            MAX(o.export_contract_no) AS export_contract_no,
            MAX(o.customer_service_manager) AS customer_service_manager,
            MAX(o.sales_manager) AS sales_manager,
            MAX(o.customs_number) AS customs_number,
            MAX(o.delivery_numbers) AS delivery_numbers,
            MAX(o.estimated_shipment_date) AS estimated_shipment_date,
            MAX(o.receive_money_date) AS receive_money_date,
            MAX(o.receiver) AS receiver,
            MAX(o.exit_clearance) AS exit_clearance,
            MAX(o.freight_amount) AS freight_amount,
            MAX(o.premium_amount) AS premium_amount,
            MAX(o.miscellaneous_amount) AS miscellaneous_amount,
            MAX(o.domestic_source_of_goods) AS domestic_source_of_goods,
            MAX(o.final_contry) AS final_contry,
            MAX(o.trading_type) AS trading_type,
            MAX(o.customs_broker_id) AS customs_broker_id,
            MAX(o.close_date) AS close_date,
            MAX(o.overseas_warehouse_name) AS overseas_warehouse_name,
            MAX(o.overseas_warehouse_address) AS overseas_warehouse_address,
            MAX(o.overseas_warehouse_country) AS overseas_warehouse_country,
            MAX(o.overseas_warehouse_platform) AS overseas_warehouse_platform,
            MAX(o.foreign_exchange_amount) AS foreign_exchange_amount,
            MAX(o.order_status) AS order_status,
            MAX(o.receive_money_flag) AS receive_money_flag,
            MAX(o.pay_money_flag) AS pay_money_flag,
            MAX(o.has_pay_money_flag) AS has_pay_money_flag,
            MAX(o.logistics_service_mode) AS logistics_service_mode,
            MAX(o.del_flag) AS del_flag,
            MAX(o.remarks) AS remarks,
            MAX(o.remind_status) AS remind_status,
            MAX(d.PRODUCT_CATEGORY_NAME) AS productCategoryName
        FROM
            ORDER_INFO o
        LEFT JOIN ORDER_PRODUCT_INFO b ON o.ID = b.ORDER_INFO_ID
        LEFT JOIN PRODUCT_INFO c ON c.ID = b.PRODUCT_ID
        LEFT JOIN PRODUCT_CATEGORY_INFO d ON d.ID = c.PRODUCT_CATEGORY_INFO_ID
        WHERE
            o.del_flag = 0
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and o.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单号 -->
        <if test="orderSummaryInfoBiz.orderProtocolNo != null and orderSummaryInfoBiz.orderProtocolNo != ''">
            and o.order_protocol_no LIKE CONCAT('%', #{orderSummaryInfoBiz.orderProtocolNo,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 订单状态 -->
        <if test="orderSummaryInfoBiz.orderStatus != null">
            and o.order_status = #{orderSummaryInfoBiz.orderStatus,jdbcType=INTEGER}
        </if>
        <!-- 监管模式 -->
        <if test="orderSummaryInfoBiz.supervisionMode != null">
            and o.supervision_mode = #{orderSummaryInfoBiz.supervisionMode,jdbcType=INTEGER}
        </if>
        <!-- 贸易国（地区） -->
        <if test="orderSummaryInfoBiz.tradingCountry != null">
            and o.trading_country = #{orderSummaryInfoBiz.tradingCountry,jdbcType=VARCHAR}
        </if>
        <!-- 报关单号 -->
        <if test="orderSummaryInfoBiz.customsNumber != null and orderSummaryInfoBiz.customsNumber != ''">
            and o.customs_number LIKE CONCAT('%', #{orderSummaryInfoBiz.customsNumber,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 境外付款方 -->
        <if test="orderSummaryInfoBiz.overseasPayerInfoId != null">
            and o.overseas_payer_info_id = #{orderSummaryInfoBiz.overseasPayerInfoId,jdbcType=VARCHAR}
        </if>

        <choose>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 0">
                and (
                    o.trading_country in
                    <foreach collection="orderSummaryInfoBiz.tradingCountryList" index="index" item="tradingCountry" open="(" separator="," close=")">
                        #{tradingCountry}
                    </foreach>
                )
            </when>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 1">
                and (
                o.trading_country in
                <foreach collection="orderSummaryInfoBiz.countryL1List" index="index" item="tradingCountry" open="(" separator="," close=")">
                    #{tradingCountry}
                </foreach>
                )
            </when>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 2">
                and (
                o.trading_country in
                <foreach collection="orderSummaryInfoBiz.countryL2List" index="index" item="tradingCountry" open="(" separator="," close=")">
                    #{tradingCountry}
                </foreach>
                )
            </when>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 3">
                and (
                o.trading_country in
                <foreach collection="orderSummaryInfoBiz.countryL3List" index="index" item="tradingCountry" open="(" separator="," close=")">
                    #{tradingCountry}
                </foreach>
                )
            </when>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 4">
                and (
                d.PRODUCT_CATEGORY_NAME in
                <foreach collection="orderSummaryInfoBiz.productCategoryNameList" index="index" item="productCategoryName" open="(" separator="," close=")">
                    #{productCategoryName}
                </foreach>
                )
            </when>
            <when test="orderSummaryInfoBiz.hasRisksFlag != null and orderSummaryInfoBiz.hasRisksFlag == 5">
                and (
                    DATEDIFF(
                        o.RECEIVE_MONEY_DATE,
                        o.CREATE_TIME
                    ) >= 60
                )
            </when>
            <otherwise>
                and (
                    o.trading_country in
                    <foreach collection="orderSummaryInfoBiz.tradingCountryList" index="index" item="tradingCountry" open="(" separator="," close=")">
                        #{tradingCountry}
                    </foreach>
                    or d.PRODUCT_CATEGORY_NAME in
                    <foreach collection="orderSummaryInfoBiz.productCategoryNameList" index="index" item="productCategoryName" open="(" separator="," close=")">
                        #{productCategoryName}
                    </foreach>
                    <if test="orderSummaryInfoBiz.type != null">
                        or (
                            DATEDIFF(
                                o.RECEIVE_MONEY_DATE,
                                o.CREATE_TIME
                            ) >= 60
                        )
                    </if>
                )
            </otherwise>
        </choose>

        GROUP BY
            o.ID
        ORDER BY
            MAX(o.create_time) DESC
    </select>
    <select id="queryPageListOrderHighRisks"
            resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
        SELECT
            MAX(t.NAME) AS tenantName,
            COUNT( * ) AS record_count
        FROM
            ORDER_INFO o
        LEFT JOIN SYS_TENANT t ON t.ID = o.TENANT_ID
        WHERE
            o.del_flag = 0 AND o.IE_FLAG = #{orderSummaryInfoBiz.ieFlag} and o.tenant_id != 0
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and o.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单号 -->
        <if test="orderSummaryInfoBiz.orderProtocolNo != null and orderSummaryInfoBiz.orderProtocolNo != ''">
            and o.order_protocol_no LIKE CONCAT('%', #{orderSummaryInfoBiz.orderProtocolNo,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 订单状态 -->
        <if test="orderSummaryInfoBiz.orderStatus != null">
            and o.order_status = #{orderSummaryInfoBiz.orderStatus,jdbcType=INTEGER}
        </if>
        <!-- 监管模式 -->
        <if test="orderSummaryInfoBiz.supervisionMode != null">
            and o.supervision_mode = #{orderSummaryInfoBiz.supervisionMode,jdbcType=INTEGER}
        </if>
        <!-- 贸易国（地区） -->
        <if test="orderSummaryInfoBiz.tradingCountry != null">
            and o.trading_country = #{orderSummaryInfoBiz.tradingCountry,jdbcType=VARCHAR}
        </if>
        <!-- 报关单号 -->
        <if test="orderSummaryInfoBiz.customsNumber != null and orderSummaryInfoBiz.customsNumber != ''">
            and o.customs_number LIKE CONCAT('%', #{orderSummaryInfoBiz.customsNumber,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 境外付款方 -->
        <if test="orderSummaryInfoBiz.overseasPayerInfoId != null">
            and o.overseas_payer_info_id = #{orderSummaryInfoBiz.overseasPayerInfoId,jdbcType=VARCHAR}
        </if>

        and (
            o.trading_country in
            <foreach collection="orderSummaryInfoBiz.tradingCountryList" index="index" item="tradingCountry" open="(" separator="," close=")">
                #{tradingCountry}
            </foreach>
        )

        GROUP BY
            t.NAME
        ORDER BY
            record_count DESC
    </select>
    <select id="queryPageListForRisks" resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
        SELECT
            *
        FROM
            ORDER_INFO o
        WHERE
            o.del_flag = 0 AND o.IE_FLAG = #{orderSummaryInfoBiz.ieFlag}
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and o.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        <!-- 订单状态 -->
        <if test="orderSummaryInfoBiz.orderStatus != null">
            and o.order_status = #{orderSummaryInfoBiz.orderStatus,jdbcType=INTEGER}
        </if>
        <!-- 订单号 -->
        <if test="orderSummaryInfoBiz.orderProtocolNo != null and orderSummaryInfoBiz.orderProtocolNo != ''">
            and o.order_protocol_no LIKE CONCAT('%', #{orderSummaryInfoBiz.orderProtocolNo,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 监管模式 -->
        <if test="orderSummaryInfoBiz.supervisionMode != null">
            and o.supervision_mode = #{orderSummaryInfoBiz.supervisionMode,jdbcType=INTEGER}
        </if>
        <!-- 贸易国（地区） -->
        <if test="orderSummaryInfoBiz.tradingCountry != null">
            and o.trading_country = #{orderSummaryInfoBiz.tradingCountry,jdbcType=VARCHAR}
        </if>
        <!-- 报关单号 -->
        <if test="orderSummaryInfoBiz.customsNumber != null and orderSummaryInfoBiz.customsNumber != ''">
            and o.customs_number LIKE CONCAT('%', #{orderSummaryInfoBiz.customsNumber,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 境外付款方 -->
        <if test="orderSummaryInfoBiz.overseasPayerInfoId != null">
            and o.overseas_payer_info_id = #{orderSummaryInfoBiz.overseasPayerInfoId,jdbcType=VARCHAR}
        </if>
        ORDER BY
            o.create_time DESC
    </select>
    <select id="listCustomerOrderNums" resultType="org.jeecg.modules.business.entity.OrderCharts">
        SELECT
            MAX( t.NAME ) AS customerName,
            COUNT( * ) AS record_count,
            SUM( CASE WHEN o.IE_FLAG = 'E' THEN 1 ELSE 0 END ) AS eNum,
            SUM( CASE WHEN o.IE_FLAG = 'I' THEN 1 ELSE 0 END ) AS iNum
        FROM
            ORDER_INFO o
                LEFT JOIN SYS_TENANT t ON t.ID = o.TENANT_ID
        WHERE
            o.del_flag = 0
          AND o.tenant_id != 0
        AND o.order_status in (3,9) AND o.HAS_NORMAL_FLAG = 1 AND o.ORDER_FLAG = 1
        GROUP BY
            t.NAME
    </select>
    <select id="listCustomerExportOrderAmount" resultType="org.jeecg.modules.business.entity.OrderCharts">
        SELECT
            t.NAME AS customerName,
            SUM( his.collection_matching_amount * fo.rate ) AS exportOrdersAmount
        FROM
            order_info info
        LEFT JOIN SYS_TENANT t ON t.ID = info.TENANT_ID
        LEFT JOIN matching_history_info his ON his.order_info_id = info.id
        LEFT JOIN foreign_exchange_collection_info fo ON fo.id = his.foreign_exchange_collection_id
        WHERE
            info.IE_FLAG = 'E'
        AND info.tenant_id != 0
        AND info.order_status in (3,9) AND info.HAS_NORMAL_FLAG = 1 AND info.ORDER_FLAG = 1
        AND info.del_flag = 0
        AND info.close_date >= #{orderSummaryInfoBiz.startDate,jdbcType=VARCHAR}
        AND info.close_date <![CDATA[ <= ]]> #{orderSummaryInfoBiz.endDate,jdbcType=VARCHAR}
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and info.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        GROUP BY
            t.NAME
    </select>
    <select id="listCustomerImportOrderAmount" resultType="org.jeecg.modules.business.entity.OrderCharts">
        SELECT
            t.NAME AS customerName,
            SUM( info.TOTAL_CONTRACT_AMOUNT * info.GUESS_RATE ) AS importOrdersAmount
        FROM
            order_info info
        LEFT JOIN SYS_TENANT t ON t.ID = info.TENANT_ID
        WHERE
            info.IE_FLAG = 'I'
        AND info.tenant_id != 0
        AND info.order_status in (3,9) AND info.HAS_NORMAL_FLAG = 1 AND info.ORDER_FLAG = 1
        AND info.del_flag = 0
        AND info.close_date >= #{orderSummaryInfoBiz.startDate,jdbcType=VARCHAR}
        AND info.close_date <![CDATA[ <= ]]> #{orderSummaryInfoBiz.endDate,jdbcType=VARCHAR}
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and info.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        GROUP BY
            t.NAME
    </select>
    <select id="listCustomerOrderTradingCountry"
            resultType="org.jeecg.modules.business.entity.OrderSummaryInfoBiz">
        SELECT
            MAX( t.NAME ) AS customerName,
            o.TRADING_COUNTRY,
            COUNT( * ) AS totalNum,
            SUM( CASE WHEN o.IE_FLAG = 'E' THEN 1 ELSE 0 END ) AS eNum,
            SUM( CASE WHEN o.IE_FLAG = 'I' THEN 1 ELSE 0 END ) AS iNum
        FROM
            ORDER_INFO o
                LEFT JOIN SYS_TENANT t ON t.ID = o.TENANT_ID
        WHERE
            o.del_flag = 0
            AND o.tenant_id != 0
            AND o.order_status in (3,9) AND o.HAS_NORMAL_FLAG = 1 AND o.ORDER_FLAG = 1
            AND o.close_date >= #{orderSummaryInfoBiz.startDate,jdbcType=VARCHAR}
            AND o.close_date <![CDATA[ <= ]]> #{orderSummaryInfoBiz.endDate,jdbcType=VARCHAR}
        <!-- 租户id -->
        <if test="orderSummaryInfoBiz.tenantId != null">
            and o.tenant_id = #{orderSummaryInfoBiz.tenantId,jdbcType=INTEGER}
        </if>
        GROUP BY
            t.NAME,
            o.TRADING_COUNTRY
        ORDER BY
            t.NAME
    </select>
    <select id="listDomesticSuppliersCotactInfoByOrder"
            resultType="org.jeecg.modules.business.entity.DomesticSuppliersCotactInfo">
        select dsc.*
        from ORDER_INFO od left join DOMESTIC_SUPPLIERS_INFO ds
        on od.OVERSEAS_PAYER_INFO_ID=ds.id
        left join DOMESTIC_SUPPLIERS_COTACT_INFO dsc
        on ds.id=dsc.SUPPLIERS_INFO_ID
        where od.id=#{id}
    </select>

</mapper>
