<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.CommonMapper">
    <insert id="insertSysTenant">
        INSERT INTO `sys_tenant`
        (
            `ID`,
            `NAME`,
            `CREATE_TIME`,
            `CREATE_BY`,
            `STATUS`,
            `TYPE`
        )
        VALUES
            (
                #{id},
                #{name},
                #{createTime},
                #{createBy},
                #{status},
                #{type}
            )
    </insert>
    <insert id="insertUser">
        INSERT INTO `sys_user`
        (
            `ID`,
            `USERNAME`,
            `REALNAME`,
            `PASSWORD`,
            `SALT`,
            `STATUS`,
            `DEL_FLAG`,
            `CREATE_BY`,
            `CREATE_TIME`,
            `REL_TENANT_IDS`,
            `TENANT_ID`
        )
        VALUES
            (
                #{id},
                #{username},
                #{realname},
                #{password},
                #{salt},
                #{status},
                #{delFlag},
                #{createBy},
                #{createTime},
                #{relTenantIds},
                #{tenantId}
            )
    </insert>
    <insert id="insertUserRole">
        INSERT INTO `sys_user_role`
        (
            `ID`,
            `USER_ID`,
            `ROLE_ID`
        )
        VALUES
            (
                #{id},
                #{userId},
                #{roleId}
            )
    </insert>

    <select id="listDictQuery" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            ${code} code,
            ${name} name
        <if test="title != null and title !=''">
            ,${title} title
        </if>
        FROM
            ${table}
        WHERE
            1 = 1
        <if test="suffix != null and suffix !=''">
            AND #{suffix}
        </if>
    </select>

    <select id="listDict" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            a.ITEM_VALUE code,
            a.ITEM_TEXT name
        FROM
            `sys_dict_item` a
                LEFT JOIN sys_dict b ON a.DICT_ID = b.ID
        WHERE
            b.DICT_CODE = #{dictCode}
    </select>
    <select id="getUserByName" resultType="org.jeecg.common.system.vo.LoginUser">
        select * from  sys_user  where username = #{username} and del_flag = 0
    </select>
    <select id="getEnterpriseInfoByCond" resultType="org.jeecg.modules.business.entity.EnterpriseInfo">
        SELECT
            *
        FROM
            `enterprise_info`
        WHERE
            ( TENANT_ID = #{searchText} OR ENTERPRISE_FULL_NAME = #{searchText} )
          AND TENANT_ID != 0
	    LIMIT 1
    </select>
    <select id="getDepartNameByTenantId" resultType="java.lang.String">
        SELECT
            `NAME`
        FROM
            `sys_tenant`
        WHERE
            ID = #{tenantId}
          AND `STATUS` = '1'
    </select>
    <select id="getUnitsQuery" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            code code,
            name name,
            item_key text
        FROM
            `erp_units`
        WHERE
            CODE = #{code}
            LIMIT 1
    </select>
    <select id="getUnitsQueryByKey" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            code code,
            name name,
            item_key text
        FROM
            `erp_units`
        WHERE
            item_key = #{code}
            LIMIT 1
    </select>
    <select id="getCityportsByEnName" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            CITYPORT_CODE CODE,
            CNNAME NAME,
            ENNAME TEXT
        FROM
            `erp_cityports`
        WHERE
            ENNAME LIKE CONCAT('%', #{enName}, '%')
            AND ISENABLED = 1
            LIMIT 1
    </select>
    <select id="getEnterpriseInfoBySwid" resultType="org.jeecg.modules.business.entity.EnterpriseInfo">
        SELECT
            *
        FROM
            `enterprise_info`
        WHERE
            SWID = #{swid}
          AND TENANT_ID != 0
	    LIMIT 1
    </select>
    <select id="getUserByUsername" resultType="org.jeecg.common.system.vo.SysUser">
        SELECT
            *
        FROM
            `sys_user`
        WHERE
            USERNAME = #{username}
    </select>
    <select id="queryEffectiveTenant" resultType="org.jeecg.common.system.vo.SysTenant">
        SELECT
            *
        FROM
            `sys_tenant`
        WHERE
            ID IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND `STATUS` = 1
    </select>
    <select id="getRolesByUserId" resultType="org.jeecg.common.system.vo.SysRole">
        SELECT
            r.ID,
            MAX(r.ROLE_NAME) ROLE_NAME,
            MAX(r.ROLE_CODE) ROLE_CODE
        FROM
            SYS_ROLE r
                LEFT JOIN SYS_USER_ROLE sur ON sur.ROLE_ID = r.ID
                LEFT JOIN SYS_USER u ON u.ID = sur.USER_ID
        WHERE
            u.ID = #{id}
        GROUP BY
            r.ID
    </select>
    <select id="getSysRoleById" resultType="org.jeecg.common.system.vo.SysRole">
        SELECT
            *
        FROM
            `sys_role`
        WHERE
            ID = #{id}
    </select>
    <select id="getTenantByName" resultType="org.jeecg.common.system.vo.SysTenant">
        SELECT
            *
        FROM
            `sys_tenant`
        WHERE
            `NAME` = #{departName}
    </select>
    <select id="getByTenantIdAndCreateBy" resultType="org.jeecg.common.system.vo.SysUser">
        SELECT
            *
        FROM
            `sys_user`
        WHERE
            TENANT_ID = #{tenantId}
        <if test="createBy != null and createBy !=''">
            AND CREATE_BY = #{createBy}
        </if>
            LIMIT 1
    </select>
    <select id="getEnterpriseInfoById" resultType="org.jeecg.modules.business.entity.EnterpriseInfo">
        SELECT
            *
        FROM
            `enterprise_info`
        WHERE
            ID = #{id}
            LIMIT 1
    </select>
    <select id="getTenantById" resultType="org.jeecg.common.system.vo.Tenant">
        SELECT
            *
        FROM
            `sys_tenant`
        WHERE
            ID = #{customerId}
    </select>
    <update id="updateSysTenant">
        UPDATE sys_tenant
        SET `name` = #{name}
        WHERE ID = #{tenantId}
    </update>
    <select id="listLog" resultType="org.jeecg.modules.business.entity.AiLog">
        SELECT
            *
        FROM
            `ai_log`
        WHERE
            LOG_TYPE IN (3, 4, 5, 6)
          AND IS_SUCCESS = 0
        ORDER BY
            CREATE_TIME DESC
    </select>
    <select id="getStatusRecords" resultType="org.jeecg.modules.business.entity.AiLog">
        SELECT
            *
        FROM
            `ai_log`
        WHERE
            LOG_TYPE = #{logType}
          AND SOURCE_ID = #{sourceId}
        ORDER BY
            CREATE_TIME DESC
    </select>
    <select id="getDictItemByKey" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            a.item_value code,
            a.item_text text
        FROM
            `sys_dict_item` a
        LEFT JOIN sys_dict b ON a.DICT_ID = b.ID
        WHERE
            b.DICT_CODE = #{dictCode}
        <if test="itemValue != null and itemValue !=''">
            AND a.ITEM_VALUE = #{itemValue}
        </if>
        <if test="itemText != null and itemText !=''">
            AND a.ITEM_TEXT = #{itemText}
        </if>
        LIMIT 1
    </select>
    <select id="getCustomerTypeByUserId" resultType="java.lang.String">
        SELECT
            b.`NAME`
        FROM
            `sys_role` a
                LEFT JOIN sys_tenant b ON b.TYPE = a.ID
        WHERE
            b.ID = (SELECT TENANT_ID FROM sys_user WHERE ID = #{id})
          AND a.ROLE_NAME LIKE '%报关行%'
    </select>
    <select id="getDictItemByKeyLike" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            a.item_value code,
            a.item_text text
        FROM
            `sys_dict_item` a
        LEFT JOIN sys_dict b ON a.DICT_ID = b.ID
        WHERE
            b.DICT_CODE = #{dictCode}
            <if test="itemValue != null and itemValue !=''">
                AND a.ITEM_VALUE LIKE CONCAT('%', #{itemValue}, '%')
            </if>
            <if test="itemText != null and itemText !=''">
                AND a.ITEM_TEXT LIKE CONCAT('%', #{itemText}, '%')
            </if>
        LIMIT 1
    </select>
    <select id="getDictItemBySearchText" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            *
        FROM
            `sys_dict_item` a
                LEFT JOIN sys_dict b ON a.DICT_ID = b.ID
        WHERE
            b.DICT_CODE = #{dictCode}
          AND (
            a.ITEM_VALUE = #{searchText}
                OR a.ITEM_TEXT = #{searchText})
    </select>
    <select id="getEnterpriseInfoByTenantId" resultType="org.jeecg.modules.business.entity.EnterpriseInfo">
        SELECT
            *
        FROM
            `enterprise_info`
        WHERE
            TENANT_ID = #{tenantId}
            LIMIT 1
    </select>
    <select id="getErpPackagesTypesByName" resultType="org.jeecg.modules.business.entity.ErpPackagesTypes">
        SELECT
            *
        FROM
            `erp_packages_types`
        WHERE
            ISENABLED = 1
          AND `NAME` LIKE CONCAT('%', #{packsKinds}, '%')
        LIMIT 1
    </select>
    <select id="getCurrencyCode" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            `CODE` code,
            CURRENCY text,
            `NAME` name
        FROM
            `erp_currencies`
        WHERE
            CURRENCY = #{searchText}
           OR `NAME` = #{searchText}
           OR `CODE` = #{searchText}
            LIMIT 1
    </select>
    <select id="getUserByClientId" resultType="org.jeecg.common.system.vo.LoginUser">
        SELECT
            *
        FROM
            `sys_user`
        WHERE
            ID = #{clientId}
    </select>
    <select id="getLogContent521" resultType="org.jeecg.modules.business.entity.AiLog">
        SELECT
            *
        FROM
            `ai_log`
        WHERE
            LOG_TYPE = 521
          AND REQUEST_TYPE = '2'
          AND SOURCE_ID = #{id}
        ORDER BY
            CREATE_TIME DESC
            LIMIT 1
    </select>
    <select id="getErpUnitsCode" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            `CODE` CODE,
            ITEM_KEY TEXT,
            `NAME` NAME
        FROM
            `erp_units`
        WHERE
            `CODE` = #{searchText}
           OR `NAME` = #{searchText}
           OR `ITEM_KEY` = #{searchText}
            LIMIT 1
    </select>
    <select id="getSocialCode" resultType="java.lang.String">
        SELECT
            UNIFIED_SOCIAL_CREDIT_CODE
        FROM
            `enterprise_info`
        WHERE
            TENANT_ID = (
                SELECT
                    TENANT_ID
                FROM
                    sys_user
                WHERE
                    ID = #{id}
            )
        LIMIT 1
    </select>
</mapper>
