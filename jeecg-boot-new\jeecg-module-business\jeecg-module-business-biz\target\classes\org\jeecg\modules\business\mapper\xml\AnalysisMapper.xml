<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.AnalysisMapper">
    <!--    查询订单年度总销售额-->
    <select id="getTotalSales" parameterType="java.util.HashMap"
            resultType="BigDecimal">
        SELECT
        SUM( his.collection_matching_amount * fo.rate ) AS amount
        FROM
        order_info info
        left join matching_history_info his ON his.order_info_id = info.id
        left join foreign_exchange_collection_info fo ON fo.id = his.foreign_exchange_collection_id
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.receive_money_flag = 1
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND info.IE_FLAG = 'E'
    </select>
    <!--    查询订单年度总销售额（每个月）-->
    <select id="getTotalSalesBar" parameterType="Map" resultType="Map">
        SELECT MONTH
        ,
        SUM( amount ) as amount
        FROM
        (
        SELECT DATE_FORMAT(info.close_date, '%m') AS MONTH,
        SUM( his.collection_matching_amount * fo.rate ) AS amount
        FROM
        order_info info
        LEFT JOIN matching_history_info his ON his.order_info_id = info.id
        LEFT JOIN foreign_exchange_collection_info fo ON fo.id = his.foreign_exchange_collection_id
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.receive_money_flag = 1
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        GROUP BY
        info.close_date
        ORDER BY
        info.close_date	) a
        GROUP BY
        MONTH
    </select>
    <!--    查询订单年度总订单量-->
    <select id="getCountOrder" parameterType="Map" resultType="Integer">
        SELECT count(*) FROM
        order_info info
        WHERE
        info.CREATE_TIME >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.CREATE_TIME &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
    </select>
    <select id="getCountDecI" parameterType="Map" resultType="Integer">
        SELECT count(*) FROM
            dec_head
        WHERE
            APP_DATE >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND APP_DATE &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND TENANT_ID = #{tenantId,jdbcType=VARCHAR}
          AND IE_FLAG = 'I'
    </select>
    <select id="getCountDecE" parameterType="Map" resultType="Integer">
        SELECT count(*) FROM
            dec_head
        WHERE
            APP_DATE >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND APP_DATE &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND TENANT_ID = #{tenantId,jdbcType=VARCHAR}
          AND IE_FLAG = 'E'
    </select>

    <!--    查询订单年度总订单量（每个月）-->
    <select id="getCountOrderBar" parameterType="Map" resultType="Map">
        SELECT DATE_FORMAT(info.close_date, '%m') AS MONTH,
        count( * ) AS count
        FROM
        order_info info
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        GROUP BY
        info.close_date
        ORDER BY
        info.close_date
    </select>
    <!--    查询订单本年度报关金额统计(每个月)-->
    <select id="getShipmentAmountList" parameterType="Map" resultType="Map">
        SELECT
        info.customs_declaration_currency AS currency,
        sum( info.total_contract_amount ) AS total,
        DATE_FORMAT(info.close_date, '%Y-%m') AS closeM
        FROM
        order_info info
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND info.IE_FLAG = 'E'
        GROUP BY
            DATE_FORMAT(info.close_date, '%Y-%m'),
        info.customs_declaration_currency
        ORDER BY
            DATE_FORMAT(info.close_date, '%Y-%m')
    </select>
    <!--    查询订单本年度报关金额统计(每个月)(所有币种)-->
    <select id="getShipmentAmountListCurrency" parameterType="Map" resultType="String">
        SELECT
        DISTINCT (ec.currency )as currencyText
        FROM
        order_info info
        LEFT JOIN erp_currencies ec ON ec.CODE = info.customs_declaration_currency
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.order_status in (3,9)
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND info.ie_flag = 'E'
    </select>
    <!--    查询订单年度盈利收入（每个月）-->
    <select id="getOrderIncome" parameterType="Map" resultType="Map">
        SELECT DATE_FORMAT(info.close_date, '%m') AS MONTH,
        sum( his.matching_amount * f.rate ) as amount
        FROM
        order_info info
        LEFT JOIN matching_history_info his ON his.order_info_id = info.id
        LEFT JOIN foreign_exchange_collection_info f ON f.id = his.foreign_exchange_collection_id
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND IE_FLAG = 'E'
        AND info.order_status in (3,9)
        AND info.pay_money_flag = 1
        AND info.receive_money_flag = 1
        GROUP BY
            DATE_FORMAT(info.close_date, '%m')
        ORDER BY
            MONTH
    </select>
    <!--    查询订单年度盈利支出（每个月）-->
    <select id="getOrderOut" parameterType="Map" resultType="Map">
        SELECT DATE_FORMAT(info.close_date, '%m') AS MONTH,
        sum( pay.payment_amount ) as amount
        FROM
        order_info info
        LEFT JOIN order_payment_info pay ON pay.order_info_id = info.id
        AND pay.del_flag = 0
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND info.order_status in (3,9)
        AND info.pay_money_flag = 1
        AND info.receive_money_flag = 1
        GROUP BY
        info.close_date
        ORDER BY
        info.close_date
    </select>

    <!--    查出报关单(1039)票量和月份(每个月)-->
    <select id="getNumberOfMarketProcurementCustoms" parameterType="Map" resultType="Map">
        SELECT DATE_FORMAT(APP_DATE, '%m') AS month,
        COUNT (*) as amount
         FROM dec_head
        WHERE
            APP_DATE >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND APP_DATE &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
          AND TENANT_ID = #{tenantId,jdbcType=VARCHAR}
        AND TRADE_TYPE_CODE = '1039'
        GROUP BY
            DATE_FORMAT(APP_DATE, '%m')
        ORDER BY
            DATE_FORMAT(APP_DATE, '%m')
    </select>
    <!--    查询订单年度贸易国 -->
    <select id="getTradingCountryPie" parameterType="Map" resultType="Map">
        SELECT
        count( info.id ) AS orderCount,
        sum( info.total_contract_amount ) AS total,
        ec.currency,
        erc.NAME
        FROM
        order_info info
        LEFT JOIN erp_currencies ec ON ec.CODE = info.customs_declaration_currency
        LEFT JOIN erp_countries erc ON erc.CODE = info.trading_country
        WHERE
        info.close_date >= STR_TO_DATE(#{firstDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.close_date &lt;= STR_TO_DATE(#{lastDay,jdbcType=VARCHAR}, '%Y-%m-%d')
        AND info.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND info.del_flag = 0
        AND info.order_status in (3,9)
        GROUP BY
        ec.currency,
        erc.NAME
        ORDER BY
        erc.NAME
    </select>
</mapper>
