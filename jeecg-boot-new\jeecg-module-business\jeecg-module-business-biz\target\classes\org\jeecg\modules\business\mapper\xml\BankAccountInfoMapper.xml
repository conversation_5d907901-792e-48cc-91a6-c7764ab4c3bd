<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.BankAccountInfoMapper">

    <!--  -->
    <select id="getCollectionBankList" parameterType="org.jeecg.modules.business.entity.BankAccountInfo"
            resultType="org.jeecg.modules.business.entity.BankAccountInfo">

        SELECT
        b.id,
        b.bank_name,
        b.bank_account,
        b.bank_account_name,
        b.bank_address,
        b.bank_address_detail,
        b.swift_number,
        b.currency_type
        FROM
        bank_account_info b

        <where>
            b.del_flag = 0
            and b.tenant_id  = #{tenantId,jdbcType=INTEGER}
            and b.info_code = #{infoCode,jdbcType=VARCHAR}
            and b.is_default_account = 1

        </where>
    </select>

    <select id="getBankListById" parameterType="org.jeecg.modules.business.entity.BankAccountInfo"
            resultType="org.jeecg.modules.business.entity.BankAccountInfo">

        SELECT
        b.bank_name,
        b.bank_account,
        b.bank_account_name,
        b.bank_address,
        b.bank_address_detail,
        b.swift_number,
        b.currency_type
        FROM
        bank_account_info b

        <where>
            b.del_flag = 0
            and b.tenant_id  = #{tenantId,jdbcType=INTEGER}
            and b.info_code = #{infoCode,jdbcType=VARCHAR}

        </where>
    </select>

</mapper>