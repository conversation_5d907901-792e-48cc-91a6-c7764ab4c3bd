<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.AppHeadTypeMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.AppHeadType">
        SELECT
            a.*,
            (
                SELECT
                    edi.NOTE
                FROM
                    EDI_STATUS_HISTORY edi
                WHERE
                    a.SEQ_NO = edi.SEQ_NO
                AND a.SEQ_NO != ''
                ORDER BY
                    edi.RECEIVER_TIME DESC,
                    edi.PERFORM_SORT DESC,
                    edi.CODE
                    LIMIT 1
            )  AS ediNote
        FROM
            app_head_type a
        <where>
            <if test="appHeadType.areainEt != null and appHeadType.areainEt != ''">
                AND a.AREAIN_ET LIKE CONCAT('%', #{appHeadType.areainEt}, '%')
            </if>
            <if test="appHeadType.seqNo != null and appHeadType.seqNo != ''">
                AND a.SEQ_NO LIKE CONCAT('%', #{appHeadType.seqNo}, '%')
            </if>
            <if test="appHeadType.etpsPreentNo != null and appHeadType.etpsPreentNo != ''">
                AND a.ETPS_PREENT_NO LIKE CONCAT('%', #{appHeadType.etpsPreentNo}, '%')
            </if>
            <if test="appHeadType.sasDclNo != null and appHeadType.sasDclNo != ''">
                AND a.SAS_DCL_NO LIKE CONCAT('%', #{appHeadType.sasDclNo}, '%')
            </if>
            <if test="appHeadType.areainEtpsno != null and appHeadType.areainEtpsno != ''">
                AND a.AREAIN_ETPSNO LIKE CONCAT('%', #{appHeadType.areainEtpsno}, '%')
            </if>
            <if test="appHeadType.businessTypecd != null and appHeadType.businessTypecd != ''">
                AND a.BUSINESS_TYPECD = #{appHeadType.businessTypecd}
            </if>
            <if test="appHeadType.dclTbStucd != null and appHeadType.dclTbStucd != ''">
                AND a.DCL_TB_STUCD = #{appHeadType.dclTbStucd}
            </if>
            <if test="appHeadType.appHeadStatus != null and appHeadType.appHeadStatus != ''">
                AND a.APP_HEAD_STATUS = #{appHeadType.appHeadStatus}
            </if>
            <if test="appHeadType.starCreateDate != null and appHeadType.starCreateDate != ''">
                AND date_format(a.CREATE_DATE, '%Y-%m-%d') &gt;= #{appHeadType.starCreateDate}
            </if>
            <if test="appHeadType.lastCreateDate != null and appHeadType.lastCreateDate != ''">
                AND date_format(a.CREATE_DATE, '%Y-%m-%d') &lt;= #{appHeadType.lastCreateDate}
            </if>
        </where>
        ORDER BY a.CREATE_DATE DESC
    </select>
</mapper>
