<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.MiniappBannerInfoMapper">
    <select id="getMiniAppBannerList" parameterType="org.jeecg.modules.business.entity.MiniappBannerInfo"
            resultType="org.jeecg.modules.business.entity.MiniappBannerInfo">

        SELECT
        id,
        banner_picture,
        banner_order

        FROM
        miniapp_banner_info

        <where>
            display_flag = 1
            and del_flag = 0
        </where>

        GROUP BY
        banner_order

        ORDER BY
        banner_order
    </select>
    <select id="getMiniAppBannerContent" parameterType="org.jeecg.modules.business.entity.MiniappBannerInfo"
            resultType="org.jeecg.modules.business.entity.MiniappBannerInfo">

        SELECT
        id,
        banner_title,
        banner_content,
        banner_author,
        banner_date

        FROM
        miniapp_banner_info

        <where>
            display_flag = 1
            and del_flag = 0
            and id = #{id,jdbcType=VARCHAR}
        </where>

    </select>
</mapper>