<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.StorageDetailMapper">

    <select id="getEmsNo" resultType="java.lang.String">
        SELECT
            a.PUTREC_NO
        FROM
            `nems_invt_head` a
                LEFT JOIN nems_invt_list b ON a.ID = b.INV_ID
        WHERE
            b.ID IN
        <foreach collection="invtListIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        LIMIT 1
    </select>
    <select id="getStorageDetailOutByCond" resultType="org.jeecg.modules.business.entity.StorageDetail">
        SELECT
            b.*
        FROM
            storage_info a
                LEFT JOIN storage_detail b ON a.STORAGE_NO = b.STORAGE_NO
        WHERE
            a.IE_FLAG = 'E'
          AND a.TYPE = '1'
          AND CONCAT( b.COP_GNO, '|', b.PN, '|', b.DETAIL_TYPE ) = #{searchCondition}
    </select>
    <select id="getStorageDetailOutByCond_" resultType="org.jeecg.modules.business.entity.StorageDetail">
        SELECT
            b.*
        FROM
            storage_info a
                LEFT JOIN storage_detail b ON a.STORAGE_NO = b.STORAGE_NO
        WHERE
            a.IE_FLAG = 'E'
          AND a.TYPE = '1'
          AND CONCAT( b.COP_GNO, '|', b.DETAIL_TYPE ) = #{searchCondition}
          AND a.TENANT_ID = #{tenantId}
    </select>
    <select id="getStorageDetailOldGoodsByCond" resultType="org.jeecg.modules.business.entity.StorageDetail">
        SELECT
            b.*
        FROM
            storage_info a
                LEFT JOIN storage_detail b ON a.STORAGE_NO = b.STORAGE_NO
        WHERE
            a.IE_FLAG = 'I'
          AND a.TYPE = '1'
          AND b.DETAIL_TYPE = '3'
          AND CONCAT( b.COP_GNO, '|', b.PN, '|', b.DETAIL_TYPE ) = #{searchCondition}
    </select>
    <select id="getOldPartsByCond" resultType="org.jeecg.modules.business.entity.StorageDetail">
        SELECT
            a.*,
            b.IE_FLAG
        FROM
            `storage_detail` a
                LEFT JOIN storage_info b ON a.STORAGE_NO = b.STORAGE_NO
        WHERE
            b.TYPE = 1
          AND a.DETAIL_TYPE = '3'
          AND a.TENANT_ID = #{tenantId}
    </select>
</mapper>
