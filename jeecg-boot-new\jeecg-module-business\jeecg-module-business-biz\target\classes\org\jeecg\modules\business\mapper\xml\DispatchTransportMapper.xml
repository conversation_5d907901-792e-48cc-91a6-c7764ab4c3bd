<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.DispatchTransportMapper">

    <update id="updateDispatchTotalCost">
        UPDATE
            DISPATCH
        SET
            TOTAL_COST = ( SELECT SUM( COST_TOTAL ) FROM DISPATCH_TRANSPORT WHERE DISPATCH_ID = #{dispatchId})
        WHERE
            id = #{dispatchId}
    </update>
</mapper>
