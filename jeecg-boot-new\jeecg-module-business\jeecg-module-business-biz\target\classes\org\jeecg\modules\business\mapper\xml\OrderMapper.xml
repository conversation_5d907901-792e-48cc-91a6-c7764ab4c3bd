<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.business.mapper.OrderMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.business.entity.Order">
        SELECT
            a.*
        FROM
            `order` a
        LEFT JOIN order_detail b ON a.ID = b.ORDER_ID
        WHERE
            1 = 1 AND a.TENANT_ID = #{order.tenantId}
        <if test="order.orderTypeList != null and order.orderTypeList.size() > 0">
            AND a.ORDER_TYPE IN
            <foreach collection="order.orderTypeList" item="orderType" open="(" close=")" separator=",">
                #{orderType}
            </foreach>
        </if>
        <if test="order.ieFlag != null and order.ieFlag != ''">
            AND a.IE_FLAG = #{order.ieFlag}
        </if>
        <if test="order.orderNo != null and order.orderNo != ''">
            AND a.ORDER_NO LIKE CONCAT('%', #{order.orderNo}, '%')
        </if>
        <if test="order.contractNo != null and order.contractNo != ''">
            AND a.CONTRACT_NO LIKE CONCAT('%', #{order.contractNo}, '%')
        </if>
        <if test="order.buyer != null and order.buyer != ''">
            AND a.BUYER = #{order.buyer}
        </if>
        <if test="order.overseasPayerInfoId != null and order.overseasPayerInfoId != ''">
            AND a.OVERSEAS_PAYER_INFO_ID = #{order.overseasPayerInfoId}
        </if>
        <if test="order.pn != null and order.pn != ''">
            AND b.PN LIKE CONCAT('%', #{order.pn}, '%')
        </if>
        <if test="order.pnEn != null and order.pnEn != ''">
            AND b.PN_EN LIKE CONCAT('%', #{order.pnEn}, '%')
        </if>
        GROUP BY
            a.ID
        ORDER BY
            a.CREATE_DATE DESC
    </select>
    <select id="getDictByEnName" resultType="org.jeecg.modules.business.entity.dto.DictQuery">
        SELECT
            `code`,
            `name`,
            STANDARD_CODE `value`,
            ENNAME `text`
        FROM
            `erp_countries`
        WHERE
            ENNAME LIKE CONCAT('%', #{tradeCountry}, '%')
            LIMIT 1
    </select>
</mapper>
